<?php
// Database configuration
$host = "localhost";
$username = "root";
$password = "";
$database = "voting_system";

// Try to connect directly to the database first
try {
    $conn = new mysqli($host, $username, $password, $database);

    // If connection successful, return it
    if (!$conn->connect_error) {
        return $conn;
    }
} catch (Exception $e) {
    // If database doesn't exist, we'll try to create it
}

// If we're here, either the database doesn't exist or there was another connection issue
// Try connecting without specifying the database
try {
    $conn = new mysqli($host, $username, $password);

    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    // Create database if not exists
    $sql = "CREATE DATABASE IF NOT EXISTS $database";
    if ($conn->query($sql) !== TRUE) {
        die("Error creating database: " . $conn->error);
    }

    // Select the database
    $conn->select_db($database);

    // Return connection
    return $conn;
} catch (Exception $e) {
    die("Database connection error: " . $e->getMessage());
}
?>