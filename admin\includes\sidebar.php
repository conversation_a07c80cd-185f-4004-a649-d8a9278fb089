<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="sidebar-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'dashboard.php' ? 'active' : ''; ?>" href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'candidates.php' ? 'active' : ''; ?>" href="candidates.php">
                    <i class="fas fa-user-tie"></i>
                    Manage Candidates
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'students.php' ? 'active' : ''; ?>" href="students.php">
                    <i class="fas fa-users"></i>
                    View Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'user_approvals.php' ? 'active' : ''; ?>" href="user_approvals.php">
                    <i class="fas fa-user-check"></i>
                    User Approvals
                    <?php
                    // Get count of pending approvals - use a direct connection instead of require_once
                    $host = "localhost";
                    $username = "root";
                    $password = "";
                    $database = "voting_system";

                    try {
                        $sidebar_conn = new mysqli($host, $username, $password, $database);

                        if (!$sidebar_conn->connect_error) {
                            // Check if approval_status column exists
                            $column_check = $sidebar_conn->query("SHOW COLUMNS FROM students LIKE 'approval_status'");
                            if ($column_check && $column_check->num_rows > 0) {
                                $result = $sidebar_conn->query("SELECT COUNT(*) as count FROM students WHERE approval_status = 'pending'");
                                if ($result) {
                                    $pending_count = $result->fetch_assoc()['count'];
                                    if ($pending_count > 0):
                                    ?>
                                    <span class="badge badge-warning ml-1"><?php echo $pending_count; ?></span>
                                    <?php
                                    endif;
                                }
                            }
                            $sidebar_conn->close();
                        }
                    } catch (Exception $e) {
                        // Silently fail - we don't want to break the sidebar if the DB connection fails
                    }
                    ?>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'votes.php' ? 'active' : ''; ?>" href="votes.php">
                    <i class="fas fa-vote-yea"></i>
                    View Votes
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'results.php' ? 'active' : ''; ?>" href="results.php">
                    <i class="fas fa-chart-bar"></i>
                    Election Results
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>System</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'settings.php' ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </li>
        </ul>
    </div>
</nav>
