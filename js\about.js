// About page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Add animation to skills badges
    const skillBadges = document.querySelectorAll('.badge');

    skillBadges.forEach((badge, index) => {
        // Add a slight delay to each badge for a staggered animation
        setTimeout(() => {
            badge.style.opacity = '0';
            badge.style.transform = 'translateY(20px)';

            setTimeout(() => {
                badge.style.transition = 'all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                badge.style.opacity = '1';
                badge.style.transform = 'translateY(0)';
            }, 50);
        }, index * 120);

        // Add click effect to badges
        badge.addEventListener('click', function() {
            // Create a ripple effect
            this.classList.add('badge-pulse');
            setTimeout(() => {
                this.classList.remove('badge-pulse');
            }, 600);
        });
    });

    // Add hover effect to the developer image
    const developerImage = document.querySelector('.developer-image');
    if (developerImage) {
        developerImage.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'transform 0.3s ease';
            this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.2)';
        });

        developerImage.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
        });
    }

    // Add skill description tooltip functionality
    const skillsContainer = document.querySelector('.skills-container');
    if (skillsContainer) {
        // Create tooltip element
        const tooltip = document.createElement('div');
        tooltip.className = 'skill-tooltip';
        tooltip.style.position = 'absolute';
        tooltip.style.display = 'none';
        tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        tooltip.style.color = 'white';
        tooltip.style.padding = '8px 12px';
        tooltip.style.borderRadius = '6px';
        tooltip.style.fontSize = '0.9rem';
        tooltip.style.zIndex = '1000';
        tooltip.style.maxWidth = '250px';
        tooltip.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
        document.body.appendChild(tooltip);

        // Add hover events to badges
        skillBadges.forEach(badge => {
            badge.addEventListener('mouseenter', function(e) {
                const skill = this.textContent;
                let description = '';

                // Set description based on skill
                switch(skill) {
                    case 'Web Development':
                        description = 'Creating responsive, user-friendly websites using modern frameworks and technologies.';
                        break;
                    case 'Mobile App Development':
                        description = 'Building native and cross-platform mobile applications for iOS and Android.';
                        break;
                    case 'Data Analysis':
                        description = 'Extracting insights from complex datasets using statistical methods and visualization.';
                        break;
                    case 'Database Management':
                        description = 'Designing, implementing and optimizing database systems for performance and reliability.';
                        break;
                    case 'UI/UX Design':
                        description = 'Creating intuitive user interfaces and experiences that delight users.';
                        break;
                    case 'Digital Art':
                        description = 'Creating digital illustrations, graphics and visual content using modern tools.';
                        break;
                    case 'PHP':
                        description = 'Server-side scripting language for web development and dynamic content.';
                        break;
                    case 'JavaScript':
                        description = 'Programming language for creating interactive web experiences and applications.';
                        break;
                    case 'SQL':
                        description = 'Structured Query Language for managing and querying relational databases.';
                        break;
                    default:
                        description = 'A key skill in my professional toolkit.';
                }

                tooltip.textContent = description;
                tooltip.style.display = 'block';

                // Position tooltip near the badge
                const rect = this.getBoundingClientRect();
                tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                tooltip.style.top = rect.bottom + 10 + window.scrollY + 'px';
            });

            badge.addEventListener('mouseleave', function() {
                tooltip.style.display = 'none';
            });
        });
    }
});
