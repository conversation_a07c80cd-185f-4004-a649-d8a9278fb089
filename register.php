<?php
// Start session
session_start();

// Redirect if already logged in
if (isset($_SESSION['student_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection
$conn = require_once 'database/db_config.php';

$error = '';
$success = '';

// Process registration form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $full_name = trim($_POST['full_name']);
    $matric_no = trim($_POST['matric_no']);
    $department = trim($_POST['department']);
    $level = $_POST['level'];
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate input
    if (empty($full_name) || empty($matric_no) || empty($department) || empty($level) || empty($password)) {
        $error = 'All fields are required';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long';
    } else {
        // Check if matric number already exists
        $stmt = $conn->prepare("SELECT id FROM students WHERE matric_no = ?");
        $stmt->bind_param("s", $matric_no);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $error = 'Matriculation number already exists. Please log in or verify your matriculation number.';
        } else {
            // Hash password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // Check if approval_status column exists
            $result = $conn->query("SHOW COLUMNS FROM students LIKE 'approval_status'");

            if ($result->num_rows > 0) {
                // Column exists, use it in the query
                $stmt = $conn->prepare("INSERT INTO students (full_name, matric_no, department, level, password, approval_status) VALUES (?, ?, ?, ?, ?, 'pending')");
                $stmt->bind_param("sssss", $full_name, $matric_no, $department, $level, $hashed_password);
            } else {
                // Column doesn't exist, use the original query
                $stmt = $conn->prepare("INSERT INTO students (full_name, matric_no, department, level, password) VALUES (?, ?, ?, ?, ?)");
                $stmt->bind_param("sssss", $full_name, $matric_no, $department, $level, $hashed_password);

                // Also try to add the column
                $conn->query("ALTER TABLE students ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending' AFTER has_voted");
            }

            if ($stmt->execute()) {
                $success = 'Registration successful! Your account is pending approval by an administrator. You will be able to login once your account is approved.';
            } else {
                $error = 'Registration failed: ' . $conn->error;
            }
        }
    }
}

// Include header
include 'includes/header.php';
?>

<div class="register-container">
    <div class="card">
        <div class="card-header">
            <h4 class="mb-0">Student Registration</h4>
        </div>
        <div class="card-body">
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
                <div class="text-center mt-3">
                    <a href="login.php" class="btn btn-primary">Proceed to Login</a>
                </div>
            <?php else: ?>
                <form method="post" action="register.php">
                    <div class="form-group">
                        <label for="full_name">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required value="<?php echo isset($_POST['full_name']) ? htmlspecialchars($_POST['full_name']) : ''; ?>">
                    </div>

                    <div class="form-group">
                        <label for="matric_no">Matriculation Number</label>
                        <input type="text" class="form-control" id="matric_no" name="matric_no" required value="<?php echo isset($_POST['matric_no']) ? htmlspecialchars($_POST['matric_no']) : ''; ?>">
                    </div>

                    <div class="form-group">
                        <label for="department">Department</label>
                        <input type="text" class="form-control" id="department" name="department" required value="<?php echo isset($_POST['department']) ? htmlspecialchars($_POST['department']) : ''; ?>">
                    </div>

                    <div class="form-group">
                        <label for="level">Level</label>
                        <select class="form-control" id="level" name="level" required>
                            <option value="">Select Level</option>
                            <option value="ND1" <?php echo (isset($_POST['level']) && $_POST['level'] === 'ND1') ? 'selected' : ''; ?>>ND1</option>
                            <option value="ND2" <?php echo (isset($_POST['level']) && $_POST['level'] === 'ND2') ? 'selected' : ''; ?>>ND2</option>
                            <option value="HND1" <?php echo (isset($_POST['level']) && $_POST['level'] === 'HND1') ? 'selected' : ''; ?>>HND1</option>
                            <option value="HND2" <?php echo (isset($_POST['level']) && $_POST['level'] === 'HND2') ? 'selected' : ''; ?>>HND2</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">Confirm Password</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="confirm_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-block">Register</button>
                </form>

                <div class="text-center mt-3">
                    <p>Already have an account? <a href="login.php">Login here</a></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add JavaScript for password toggle -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggleButtons = document.querySelectorAll('.toggle-password');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const passwordInput = document.getElementById(targetId);
            const icon = this.querySelector('i');

            // Toggle password visibility
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
});
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
