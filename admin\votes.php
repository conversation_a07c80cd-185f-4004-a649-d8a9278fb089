<?php
// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection
$conn = require_once '../database/db_config.php';

// Handle search and filter
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$position_filter = isset($_GET['position']) ? $_GET['position'] : 'all';
$date_filter = isset($_GET['date']) ? $_GET['date'] : '';

// Prepare query based on search and filter
$query = "SELECT v.id, s.full_name as student_name, s.matric_no, c.full_name as candidate_name, 
          p.position_name, v.created_at
          FROM votes v
          JOIN students s ON v.student_id = s.id
          JOIN candidates c ON v.candidate_id = c.id
          JOIN positions p ON v.position_id = p.id";

$where_clauses = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_clauses[] = "(s.full_name LIKE ? OR s.matric_no LIKE ? OR c.full_name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'sss';
}

if ($position_filter !== 'all') {
    $where_clauses[] = "p.id = ?";
    $params[] = $position_filter;
    $param_types .= 'i';
}

if (!empty($date_filter)) {
    $where_clauses[] = "DATE(v.created_at) = ?";
    $params[] = $date_filter;
    $param_types .= 's';
}

if (!empty($where_clauses)) {
    $query .= " WHERE " . implode(' AND ', $where_clauses);
}

$query .= " ORDER BY v.created_at DESC";

// Prepare and execute statement
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
$votes = [];

while ($vote = $result->fetch_assoc()) {
    $votes[] = $vote;
}

// Get all positions for filter
$positions = [];
$sql = "SELECT id, position_name FROM positions ORDER BY id";
$positions_result = $conn->query($sql);

while ($position = $positions_result->fetch_assoc()) {
    $positions[] = $position;
}

// Include admin header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main content -->
        <main class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
            <h1 class="h2 mb-4">View Votes</h1>
            
            <!-- Search and filter form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" action="votes.php" class="form-row">
                        <div class="form-group col-md-4">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="Search by student, matric no, or candidate" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="position">Position</label>
                            <select class="form-control" id="position" name="position">
                                <option value="all" <?php echo $position_filter === 'all' ? 'selected' : ''; ?>>All Positions</option>
                                <?php foreach ($positions as $position): ?>
                                    <option value="<?php echo $position['id']; ?>" <?php echo $position_filter == $position['id'] ? 'selected' : ''; ?>>
                                        <?php echo $position['position_name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="date">Date</label>
                            <input type="date" class="form-control" id="date" name="date" value="<?php echo $date_filter; ?>">
                        </div>
                        <div class="form-group col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary mr-2">Apply</button>
                            <a href="votes.php" class="btn btn-secondary">Clear</a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Votes list -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Votes</h5>
                    <span class="badge badge-primary"><?php echo count($votes); ?> votes found</span>
                </div>
                <div class="card-body">
                    <?php if (count($votes) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Matric No</th>
                                        <th>Position</th>
                                        <th>Candidate</th>
                                        <th>Date & Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($votes as $vote): ?>
                                        <tr>
                                            <td><?php echo $vote['student_name']; ?></td>
                                            <td><?php echo $vote['matric_no']; ?></td>
                                            <td><?php echo $vote['position_name']; ?></td>
                                            <td><?php echo $vote['candidate_name']; ?></td>
                                            <td><?php echo date('M d, Y H:i:s', strtotime($vote['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-center">No votes found matching your criteria.</p>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
// Include admin footer
include 'includes/footer.php';
?>
