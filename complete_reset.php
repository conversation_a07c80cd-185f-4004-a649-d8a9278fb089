<?php
// Start session
session_start();

// Redirect if already logged in
if (isset($_SESSION['student_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection and functions
$conn = require_once 'database/db_config.php';
require_once 'includes/functions.php';

$error = '';
$success = '';
$token = isset($_GET['token']) ? $_GET['token'] : '';
$request = false;

// Validate token
if (!empty($token)) {
    $request = getPasswordResetRequest($token);

    if (!$request) {
        $error = 'Invalid reset token';
        $token = '';
    } elseif ($request['status'] === 'completed') {
        $error = 'This password reset link has already been used. Please request a new one if needed.';
        $token = '';
    } elseif ($request['status'] === 'rejected') {
        $error = 'Your password reset request has been rejected. Please contact an administrator for assistance.';
        $token = '';
    } elseif ($request['status'] !== 'approved') {
        $error = 'Your password reset request is still pending approval. Please check back later.';
        $token = '';
    }
}

// Process password reset
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($token)) {
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate input
    if (empty($password) || empty($confirm_password)) {
        $error = 'Please enter both password fields';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long';
    } else {
        // Get request again to ensure it's still valid
        $request = getPasswordResetRequest($token);

        if (!$request || $request['status'] !== 'approved') {
            $error = 'Invalid or expired reset token';
        } else {
            // Update password
            $student_id = $request['student_id'];
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            $stmt = $conn->prepare("UPDATE students SET password = ? WHERE id = ?");
            $stmt->bind_param("si", $hashed_password, $student_id);

            if ($stmt->execute()) {
                // Mark request as completed
                updatePasswordResetStatus($token, 'completed');

                $success = 'Your password has been reset successfully!';
                $token = ''; // Clear token to hide the form
            } else {
                $error = 'Failed to reset password: ' . $conn->error;
            }
        }
    }
}

// Include header
include 'includes/header.php';
?>

<div class="login-container">
    <div class="card">
        <div class="card-header">
            <h4 class="mb-0">Reset Password</h4>
        </div>
        <div class="card-body">
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
                <div class="text-center mt-3">
                    <a href="login.php" class="btn btn-primary">Login with your new password</a>
                </div>
            <?php elseif (!empty($token) && $request): ?>
                <p>Please enter your new password below:</p>

                <form method="post" action="complete_reset.php?token=<?php echo htmlspecialchars($token); ?>">
                    <div class="form-group">
                        <label for="password">New Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>

                    <button type="submit" class="btn btn-primary btn-block">Reset Password</button>
                </form>
            <?php else: ?>
                <div class="alert alert-info">
                    <p>To reset your password, you need to:</p>
                    <ol>
                        <li>Request a password reset from the <a href="reset_password.php">reset password page</a></li>
                        <li>Wait for an administrator to approve your request</li>
                        <li>Complete the password reset using the link provided</li>
                    </ol>
                </div>

                <div class="text-center mt-3">
                    <a href="reset_password.php" class="btn btn-primary">Request Password Reset</a>
                    <a href="login.php" class="btn btn-secondary ml-2">Back to Login</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
