<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deploy E-Voting System - Ogbonnaya Onu Polytechnic</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .deploy-container {
            max-width: 800px;
            margin: 50px auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .deploy-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .deploy-header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .deploy-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .deploy-content {
            padding: 40px 30px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .step-number.active {
            background: #007bff;
            color: white;
        }

        .step-number.completed {
            background: #28a745;
            color: white;
        }

        .step-arrow {
            color: #dee2e6;
            margin: 0 10px;
        }

        .deploy-step {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .deploy-step.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
            position: relative;
            padding-left: 30px;
        }

        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .btn-deploy {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: bold;
            border-radius: 50px;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(40,167,69,0.3);
        }

        .btn-deploy:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(40,167,69,0.4);
            color: white;
        }

        .btn-deploy:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-container {
            margin: 30px 0;
        }

        .progress {
            height: 8px;
            border-radius: 10px;
            background: #e9ecef;
        }

        .progress-bar {
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            display: none;
        }

        .status-message.show {
            display: block;
            animation: slideIn 0.5s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        .admin-form {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        .password-strength {
            margin-top: 10px;
        }

        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            margin-top: 5px;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        @media (max-width: 768px) {
            .deploy-container {
                margin: 20px;
                border-radius: 15px;
            }
            
            .deploy-header {
                padding: 30px 20px;
            }
            
            .deploy-header h1 {
                font-size: 2rem;
            }
            
            .deploy-content {
                padding: 30px 20px;
            }
            
            .step-indicator {
                flex-direction: column;
                align-items: center;
            }
            
            .step {
                margin: 10px 0;
            }
            
            .step-arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="deploy-container">
        <div class="deploy-header">
            <h1><i class="fas fa-rocket mr-3"></i>Deploy E-Voting System</h1>
            <p>Ogbonnaya Onu Polytechnic - Automated Setup</p>
        </div>

        <div class="deploy-content">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step">
                    <div class="step-number active" id="step1">1</div>
                    <span>Initialize</span>
                </div>
                <i class="fas fa-arrow-right step-arrow"></i>
                <div class="step">
                    <div class="step-number" id="step2">2</div>
                    <span>Database</span>
                </div>
                <i class="fas fa-arrow-right step-arrow"></i>
                <div class="step">
                    <div class="step-number" id="step3">3</div>
                    <span>Admin Setup</span>
                </div>
                <i class="fas fa-arrow-right step-arrow"></i>
                <div class="step">
                    <div class="step-number" id="step4">4</div>
                    <span>Complete</span>
                </div>
            </div>

            <!-- Step 1: Welcome -->
            <div class="deploy-step active" id="welcome-step">
                <h3 class="text-center mb-4">Welcome to the E-Voting System Deployment</h3>
                <p class="text-center text-muted mb-4">This automated deployment will set up your complete e-voting system in just a few steps.</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5>What will be installed:</h5>
                        <ul class="feature-list">
                            <li>Complete database structure</li>
                            <li>Student management system</li>
                            <li>Candidate registration</li>
                            <li>Voting mechanism</li>
                            <li>Admin control panel</li>
                            <li>Election settings</li>
                            <li>Security features</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Requirements:</h5>
                        <ul class="feature-list">
                            <li>XAMPP/WAMP running</li>
                            <li>MySQL server active</li>
                            <li>PHP 7.4 or higher</li>
                            <li>Web browser</li>
                        </ul>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button class="btn btn-deploy" onclick="startDeployment()">
                        <i class="fas fa-play mr-2"></i>Start Deployment
                    </button>
                </div>
            </div>

            <!-- Step 2: Database Creation -->
            <div class="deploy-step" id="database-step">
                <h3 class="text-center mb-4">Creating Database</h3>
                <div class="progress-container">
                    <div class="progress">
                        <div class="progress-bar bg-primary" id="db-progress" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="loading-spinner" id="db-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-3">Setting up database structure...</p>
                </div>
                <div class="status-message" id="db-status"></div>
            </div>

            <!-- Step 3: Admin Setup -->
            <div class="deploy-step" id="admin-step">
                <h3 class="text-center mb-4">Create Admin Account</h3>
                <p class="text-center text-muted mb-4">Set up your administrator credentials to manage the e-voting system.</p>
                
                <div class="admin-form">
                    <form id="admin-form">
                        <div class="form-group">
                            <label for="admin-username"><i class="fas fa-user mr-2"></i>Admin Username</label>
                            <input type="text" class="form-control" id="admin-username" name="username" required 
                                   placeholder="Enter admin username" value="admin">
                            <small class="form-text text-muted">This will be used to log into the admin panel.</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="admin-password"><i class="fas fa-lock mr-2"></i>Admin Password</label>
                            <input type="password" class="form-control" id="admin-password" name="password" required 
                                   placeholder="Enter secure password">
                            <div class="password-strength">
                                <small class="text-muted">Password strength: <span id="strength-text">Not entered</span></small>
                                <div class="strength-bar">
                                    <div class="strength-fill" id="strength-fill"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="admin-confirm"><i class="fas fa-check mr-2"></i>Confirm Password</label>
                            <input type="password" class="form-control" id="admin-confirm" name="confirm" required 
                                   placeholder="Confirm your password">
                            <small class="form-text" id="password-match"></small>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-deploy">
                                <i class="fas fa-user-shield mr-2"></i>Create Admin Account
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Step 4: Completion -->
            <div class="deploy-step" id="complete-step">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    </div>
                    <h3 class="text-success mb-4">Deployment Successful!</h3>
                    <p class="text-muted mb-4">Your e-voting system has been successfully deployed and is ready to use.</p>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-home fa-2x text-primary mb-3"></i>
                                    <h5>Student Portal</h5>
                                    <p class="small text-muted">Students can register and vote here</p>
                                    <a href="index.php" class="btn btn-primary">
                                        <i class="fas fa-external-link-alt mr-1"></i>Open Portal
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-cogs fa-2x text-success mb-3"></i>
                                    <h5>Admin Panel</h5>
                                    <p class="small text-muted">Manage elections and view results</p>
                                    <a href="admin/index.php" class="btn btn-success">
                                        <i class="fas fa-external-link-alt mr-1"></i>Open Admin
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-4">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Important:</strong> Please delete or secure the deploy.html file after successful deployment for security reasons.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="deploy.js"></script>
</body>
</html>
