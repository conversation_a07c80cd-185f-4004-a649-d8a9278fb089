<?php
// Set page title
$pageTitle = "Database Import Tool";

// Check if form is submitted
$message = '';
$messageType = '';
$adminResetSuccess = false;
$adminUsername = '';

// Database configuration
$host = "localhost";
$username = "root";
$password = "";
$database = "voting_system";

// Process admin password reset
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_admin'])) {
    try {
        // Connect to database
        $conn = new mysqli($host, $username, $password, $database);

        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }

        // Check if admins table exists
        $result = $conn->query("SHOW TABLES LIKE 'admins'");
        if ($result->num_rows == 0) {
            throw new Exception("Admins table does not exist. Please import the database first.");
        }

        // Check if admin user exists and get username
        $result = $conn->query("SELECT id, username FROM admins LIMIT 1");
        if ($result->num_rows == 0) {
            throw new Exception("No admin account found in the database. Please import the database first.");
        }

        $admin = $result->fetch_assoc();
        $adminUsername = $admin['username'];

        // Validate input
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';

        if (empty($new_password) || empty($confirm_password)) {
            throw new Exception("Please enter both password fields");
        } elseif ($new_password !== $confirm_password) {
            throw new Exception("Passwords do not match");
        } elseif (strlen($new_password) < 6) {
            throw new Exception("Password must be at least 6 characters long");
        }

        // Hash new password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

        // Update admin password
        $stmt = $conn->prepare("UPDATE admins SET password = ? WHERE username = ?");
        $stmt->bind_param("ss", $hashed_password, $adminUsername);

        if (!$stmt->execute() || $stmt->affected_rows == 0) {
            throw new Exception("Failed to reset admin password. Please try again.");
        }

        // Log the password reset
        $ip = $_SERVER['REMOTE_ADDR'];
        $date = date('Y-m-d H:i:s');
        $logMessage = "Admin password reset performed from IP: $ip on $date\n";
        @file_put_contents('admin_reset_log.txt', $logMessage, FILE_APPEND);

        $message = "Admin password has been reset successfully! You can now log in with the new password.";
        $messageType = "success";
        $adminResetSuccess = true;

    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
        $messageType = "danger";
    }
}

// Process database import
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['import'])) {
    // Database configuration
    $host = "localhost";
    $username = "root";
    $password = "";
    $database = "voting_system";

    try {
        // Step 1: Connect to MySQL server without selecting a database
        $conn = new mysqli($host, $username, $password);

        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }

        // Step 2: Create database if it doesn't exist
        $sql = "CREATE DATABASE IF NOT EXISTS $database";
        if (!$conn->query($sql)) {
            throw new Exception("Error creating database: " . $conn->error);
        }

        // Step 3: Select the database
        $conn->select_db($database);

        // Step 4: Create Students table
        $sql = "CREATE TABLE IF NOT EXISTS students (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(255) NOT NULL,
            matric_no VARCHAR(50) NOT NULL UNIQUE,
            department VARCHAR(255) NOT NULL,
            level ENUM('ND1', 'ND2', 'HND1', 'HND2') NOT NULL,
            password VARCHAR(255) NOT NULL,
            has_voted BOOLEAN DEFAULT FALSE,
            approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating students table: " . $conn->error);
        }

        // Step 5: Create Positions table
        $sql = "CREATE TABLE IF NOT EXISTS positions (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            position_name VARCHAR(255) NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating positions table: " . $conn->error);
        }

        // Step 6: Insert default positions
        $positions = ["President", "Director of Sports", "Director of Information"];
        foreach ($positions as $position) {
            $sql = "INSERT IGNORE INTO positions (position_name) VALUES ('$position')";
            $conn->query($sql);
        }

        // Step 7: Create Candidates table
        $sql = "CREATE TABLE IF NOT EXISTS candidates (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(255) NOT NULL,
            position_id INT(11) NOT NULL,
            program VARCHAR(255) NOT NULL,
            photo VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (position_id) REFERENCES positions(id)
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating candidates table: " . $conn->error);
        }

        // Step 8: Create Votes table
        $sql = "CREATE TABLE IF NOT EXISTS votes (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            student_id INT(11) NOT NULL,
            candidate_id INT(11) NOT NULL,
            position_id INT(11) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(id),
            FOREIGN KEY (candidate_id) REFERENCES candidates(id),
            FOREIGN KEY (position_id) REFERENCES positions(id),
            UNIQUE KEY unique_vote (student_id, position_id)
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating votes table: " . $conn->error);
        }

        // Step 9: Create Admin table
        $sql = "CREATE TABLE IF NOT EXISTS admins (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating admins table: " . $conn->error);
        }

        // Step 10: Insert default admin (username: admin, password: admin@2023)
        $admin_username = "admin";
        $admin_password = password_hash("admin@2023", PASSWORD_DEFAULT);
        $sql = "INSERT IGNORE INTO admins (username, password) VALUES ('$admin_username', '$admin_password')";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating admin account: " . $conn->error);
        }

        // Step 11: Create Election Settings table
        $sql = "CREATE TABLE IF NOT EXISTS election_settings (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(50) NOT NULL UNIQUE,
            setting_value TEXT NOT NULL,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating election_settings table: " . $conn->error);
        }

        // Step 12: Insert default settings
        $default_settings = [
            ['election_status', 'inactive'], // active, inactive
            ['election_title', 'Student Union Government Elections'],
            ['election_start_date', date('Y-m-d')],
            ['election_end_date', date('Y-m-d', strtotime('+1 week'))]
        ];

        foreach ($default_settings as $setting) {
            $key = $setting[0];
            $value = $setting[1];
            $sql = "INSERT IGNORE INTO election_settings (setting_key, setting_value) VALUES ('$key', '$value')";

            if (!$conn->query($sql)) {
                throw new Exception("Error inserting default settings: " . $conn->error);
            }
        }

        // Step 13: Create Password Reset Requests table
        $sql = "CREATE TABLE IF NOT EXISTS password_reset_requests (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            student_id INT(11) NOT NULL,
            token VARCHAR(255) NOT NULL,
            status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(id),
            UNIQUE KEY (student_id, status)
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating password_reset_requests table: " . $conn->error);
        }

        // Success message
        $message = "Database imported successfully! The database 'voting_system' has been created with all necessary tables.";
        $messageType = "success";

    } catch (Exception $e) {
        // Error message
        $message = "Error: " . $e->getMessage();
        $messageType = "danger";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Ogbonnaya Onu E-Voting System</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .import-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            background-color: #fff;
        }
        .btn-import {
            padding: 12px 30px;
            font-size: 18px;
            font-weight: 600;
        }
        .steps {
            margin-top: 30px;
            padding-left: 20px;
        }
        .steps li {
            margin-bottom: 10px;
        }
        .success-icon {
            font-size: 60px;
            color: #28a745;
            margin-bottom: 20px;
        }
        .error-icon {
            font-size: 60px;
            color: #dc3545;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="import-container">
            <h2 class="text-center mb-4"><?php echo $pageTitle; ?></h2>

            <?php if ($message): ?>
                <div class="text-center mb-4">
                    <?php if ($messageType === 'success'): ?>
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    <?php else: ?>
                        <div class="error-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-<?php echo $messageType; ?>">
                        <?php echo $message; ?>
                    </div>

                    <?php if ($messageType === 'success'): ?>
                        <div class="mt-4">
                            <a href="index.php" class="btn btn-primary">Go to Homepage</a>
                            <a href="admin/index.php" class="btn btn-secondary ml-2">Go to Admin Panel</a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="import-tab" data-toggle="tab" href="#import" role="tab" aria-controls="import" aria-selected="true">Import Database</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="reset-tab" data-toggle="tab" href="#reset" role="tab" aria-controls="reset" aria-selected="false">Reset Admin Password</a>
                    </li>
                </ul>

                <div class="tab-content" id="myTabContent">
                    <!-- Import Database Tab -->
                    <div class="tab-pane fade show active" id="import" role="tabpanel" aria-labelledby="import-tab">
                        <p class="lead text-center">This tool will automatically set up the database for the Ogbonnaya Onu E-Voting System.</p>

                        <div class="alert alert-info">
                            <strong>Note:</strong> Make sure your MySQL server is running before proceeding.
                        </div>

                        <h5>This will:</h5>
                        <ul class="steps">
                            <li>Create a database named 'voting_system'</li>
                            <li>Create all necessary tables</li>
                            <li>Set up default admin account (username: admin, password: admin@2023)</li>
                            <li>Configure initial system settings</li>
                        </ul>

                        <form method="post" action="importdb.php" class="text-center mt-4">
                            <button type="submit" name="import" class="btn btn-primary btn-import">
                                <i class="fas fa-database mr-2"></i> Import Database
                            </button>
                        </form>
                    </div>

                    <!-- Reset Admin Password Tab -->
                    <div class="tab-pane fade" id="reset" role="tabpanel" aria-labelledby="reset-tab">
                        <p class="lead text-center">Reset the admin password for the e-voting system.</p>

                        <div class="alert alert-warning">
                            <strong>Important:</strong> This will reset the password for the admin account. The current password will no longer work after this change.
                        </div>

                        <form method="post" action="importdb.php" class="mt-4">
                            <div class="form-group">
                                <label for="new_password">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <div class="mt-2">
                                    <span>Password Strength: </span>
                                    <span id="password-strength" class="badge badge-secondary">Not entered</span>
                                </div>
                                <small class="form-text text-muted">Password must be at least 6 characters long. For a strong password, use a mix of uppercase and lowercase letters, numbers, and special characters.</small>
                            </div>

                            <div class="form-group">
                                <label for="confirm_password">Confirm Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <div class="mt-2">
                                    <span id="password-match" class="small"></span>
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" name="reset_admin" class="btn btn-danger">
                                    <i class="fas fa-key mr-2"></i> Reset Admin Password
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        // Password strength meter
        $(document).ready(function() {
            // Show the appropriate tab based on URL hash
            var hash = window.location.hash;
            if (hash === '#reset') {
                $('#myTab a[href="#reset"]').tab('show');
            }

            // Update URL hash when tab changes
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                window.location.hash = e.target.hash;
            });

            // Password strength meter
            $('#new_password').on('input', function() {
                var password = $(this).val();
                var strength = 0;

                // Check password length
                if (password.length >= 8) {
                    strength += 1;
                }

                // Check for mixed case
                if (password.match(/[a-z]/) && password.match(/[A-Z]/)) {
                    strength += 1;
                }

                // Check for numbers
                if (password.match(/\d/)) {
                    strength += 1;
                }

                // Check for special characters
                if (password.match(/[^a-zA-Z\d]/)) {
                    strength += 1;
                }

                // Update strength meter
                var strengthMeter = $('#password-strength');
                var strengthText = '';
                var strengthClass = '';

                switch (strength) {
                    case 0:
                        strengthText = 'Very Weak';
                        strengthClass = 'badge-danger';
                        break;
                    case 1:
                        strengthText = 'Weak';
                        strengthClass = 'badge-warning';
                        break;
                    case 2:
                        strengthText = 'Medium';
                        strengthClass = 'badge-info';
                        break;
                    case 3:
                        strengthText = 'Strong';
                        strengthClass = 'badge-primary';
                        break;
                    case 4:
                        strengthText = 'Very Strong';
                        strengthClass = 'badge-success';
                        break;
                }

                // Update the strength meter
                strengthMeter.html(strengthText);
                strengthMeter.removeClass('badge-danger badge-warning badge-info badge-primary badge-success badge-secondary');
                strengthMeter.addClass(strengthClass);
            });

            // Check if passwords match
            $('#confirm_password').on('input', function() {
                var password = $('#new_password').val();
                var confirmPassword = $(this).val();

                if (password === confirmPassword) {
                    $('#password-match').html('Passwords match').removeClass('text-danger').addClass('text-success');
                } else {
                    $('#password-match').html('Passwords do not match').removeClass('text-success').addClass('text-danger');
                }
            });
        });
    </script>
</body>
</html>
