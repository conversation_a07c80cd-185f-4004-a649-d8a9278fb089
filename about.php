<?php
// Include header
include 'includes/header.php';
?>

<style>
    /* Styles from myabout.php */
    .about-container {
        max-width: 1000px;
        margin: 20px auto;
        padding: 25px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .about-content {
        display: flex;
        gap: 25px;
        align-items: flex-start;
    }

    .profile-image-container {
        flex: 1;
        text-align: center;
        max-width: 280px;
    }

    .profile-image {
        width: 100%;
        max-width: 280px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .profile-info {
        flex: 2;
    }

    .profile-name {
        font-size: 28px;
        color: #333;
        margin-bottom: 5px;
        font-weight: bold;
        display: inline-block;
        padding: 2px 8px;
        text-align: center;
    }

    .profile-subtitle {
        color: #666;
        margin-bottom: 20px;
        font-size: 16px;
        display: inline-block;
        padding: 2px 8px;
        text-align: center;
    }

    .name-container {
        text-align: center;
        width: 100%;
    }

    .profile-description {
        line-height: 1.3;
        color: #444;
        text-align: justify;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .profile-description p {
        margin-bottom: 8px;
    }

    .skills-section {
        margin-top: 20px;
        text-align: center;
    }

    .skills-title {
        font-size: 18px;
        color: #333;
        margin-bottom: 15px;
        font-weight: bold;
        display: inline-block;
        padding: 2px 8px;
    }

    .skills-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        max-width: 100%;
    }

    .skill-card {
        padding: 10px;
        border-radius: 20px;
        color: white;
        text-align: center;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.2s;
        font-size: 14px;
        box-shadow: 0 3px 6px rgba(0,0,0,0.1);
    }

    .skill-card i {
        margin-right: 6px;
        font-size: 14px;
    }

    .skill-card:hover {
        transform: translateY(-2px);
    }

    .web-dev {
        background-color: #4285F4;
    }

    .mobile-dev {
        background-color: #34A853;
    }

    .ui-design {
        background-color: #A142F4;
    }

    .data-analysis {
        background-color: #FBBC05;
    }

    .database {
        background-color: #EA4335;
    }

    .digital-art {
        background-color: #FF9900;
    }

    .php {
        background-color: #5D6CB3;
    }

    .javascript {
        background-color: #2196F3;
    }

    .sql {
        background-color: #F44336;
    }

    .back-button-container {
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }

    .back-button {
        display: inline-block;
        background-color: #2196F3;
        color: white;
        padding: 6px 15px;
        border-radius: 20px;
        text-decoration: none;
        text-align: center;
        font-weight: 500;
        transition: all 0.3s ease;
        font-size: 14px;
    }

    .back-button:hover {
        background-color: #0b7dda;
        transform: scale(1.05);
    }

    .back-button i {
        margin-right: 6px;
    }

    @media (max-width: 768px) {
        .about-content {
            flex-direction: column;
        }

        .profile-image-container {
            margin: 0 auto;
        }

        .skills-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .skills-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<!-- About Content from myabout.php -->
<div class="about-container">
    <div class="about-content">
        <div class="profile-image-container">
            <img src="images/image3.jpg" alt="Eberechukwu Charles Joseph" class="profile-image" onerror="this.src='https://via.placeholder.com/300x400?text=Eberechukwu+Charles+Joseph'; this.onerror=null;">
        </div>

        <div class="profile-info">
            <div class="name-container">
                <h1 class="profile-name">Eberechukwu Charles Joseph</h1>
                <div class="profile-subtitle">
                    <span style="color: #4285F4; font-weight: bold;">Mobile App & Web Developer</span> |
                    <span style="color: #34A853; font-weight: bold;">Data Analyst</span> |
                    <span style="color: #EA4335; font-weight: bold;">Database Manager</span> |
                    <span style="color: #FF9900; font-weight: bold;">Digital Artist</span>
                </div>
            </div>

            <div class="profile-description">
                <p>Hi, I'm Eberechukwu Charles Joseph, a passionate tech professional with a strong foundation in mobile app and web development, data analysis, and database management. I thrive in building efficient, scalable, and user-friendly digital solutions that make an impact.</p>

                <p>Beyond the code, I'm also a dedicated digital artist. When I'm not immersed in development or data, you'll often find me creating expressive paintings that blend creativity with precision.</p>

                <p>Driven by enthusiasm and commitment to excellence, I put my best into every project I take on. Whether solving data challenges, designing interfaces, or creating art, I aim for meaningful and memorable results.</p>
            </div>

            <div class="skills-section">
                <h2 class="skills-title" style="color: #333; font-weight: bold;">Skills & Expertise</h2>
                <div class="skills-grid">
                    <div class="skill-card web-dev">
                        <i class="fas fa-code"></i> Web Development
                    </div>
                    <div class="skill-card mobile-dev">
                        <i class="fas fa-mobile-alt"></i> Mobile App Development
                    </div>
                    <div class="skill-card ui-design">
                        <i class="fas fa-paint-brush"></i> UI/UX Design
                    </div>
                    <div class="skill-card data-analysis">
                        <i class="fas fa-chart-bar"></i> Data Analysis
                    </div>
                    <div class="skill-card database">
                        <i class="fas fa-database"></i> Database Management
                    </div>
                    <div class="skill-card digital-art">
                        <i class="fas fa-palette"></i> Digital Art
                    </div>
                    <div class="skill-card php">
                        <i class="fab fa-php"></i> PHP
                    </div>
                    <div class="skill-card javascript">
                        <i class="fab fa-js"></i> JavaScript
                    </div>
                    <div class="skill-card sql">
                        <i class="fas fa-server"></i> SQL
                    </div>
                </div>
            </div>

            <div class="back-button-container">
                <a href="#" class="back-button" onclick="history.back(); return false;">
                    <i class="fas fa-arrow-left"></i> Back to Home
                </a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
