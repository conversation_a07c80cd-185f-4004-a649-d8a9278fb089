<?php
// Include header
include 'includes/header.php';
?>

<style>
    .about-container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 0;
        background-color: #fff;
    }

    .hero-section {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 60px 0;
        text-align: center;
        margin-bottom: 0;
    }

    .hero-content h1 {
        font-size: 3.5rem;
        font-weight: bold;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .hero-content p {
        font-size: 1.3rem;
        margin-bottom: 30px;
        opacity: 0.95;
    }

    .hero-logo {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 4px solid white;
        margin-bottom: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }

    .content-section {
        padding: 50px 30px;
    }

    .section-title {
        font-size: 2.5rem;
        color: #007bff;
        text-align: center;
        margin-bottom: 40px;
        font-weight: bold;
        position: relative;
    }

    .section-title::after {
        content: '';
        display: block;
        width: 80px;
        height: 4px;
        background: #007bff;
        margin: 15px auto;
        border-radius: 2px;
    }

    .about-text {
        font-size: 1.1rem;
        line-height: 1.8;
        color: #444;
        text-align: justify;
        margin-bottom: 30px;
    }

    .mission-vision-section {
        background: #f8f9fa;
        padding: 50px 30px;
        margin: 0;
    }

    .mv-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-top: 30px;
    }

    .mv-card {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        text-align: center;
        transition: transform 0.3s ease;
    }

    .mv-card:hover {
        transform: translateY(-5px);
    }

    .mv-card i {
        font-size: 3rem;
        color: #007bff;
        margin-bottom: 20px;
    }

    .mv-card h3 {
        font-size: 1.8rem;
        color: #333;
        margin-bottom: 20px;
        font-weight: bold;
    }

    .mv-card p {
        font-size: 1rem;
        line-height: 1.6;
        color: #666;
    }

    .programs-section {
        padding: 50px 30px;
        background: white;
    }

    .programs-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
        margin-top: 30px;
    }

    .program-card {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 12px;
        border-left: 5px solid #007bff;
        transition: all 0.3s ease;
    }

    .program-card:hover {
        background: white;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        transform: translateY(-3px);
    }

    .program-card h4 {
        color: #007bff;
        font-size: 1.3rem;
        margin-bottom: 15px;
        font-weight: bold;
    }

    .program-card p {
        color: #666;
        line-height: 1.5;
        margin: 0;
    }

    .stats-section {
        background: #007bff;
        color: white;
        padding: 50px 30px;
        text-align: center;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 30px;
        margin-top: 30px;
    }

    .stat-item h3 {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .stat-item p {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .back-button-container {
        text-align: center;
        padding: 30px;
        background: #f8f9fa;
    }

    .back-button {
        display: inline-block;
        background-color: #007bff;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .back-button:hover {
        background-color: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        color: white;
        text-decoration: none;
    }

    .back-button i {
        margin-right: 8px;
    }

    @media (max-width: 768px) {
        .hero-content h1 {
            font-size: 2.5rem;
        }

        .hero-content p {
            font-size: 1.1rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .content-section, .mission-vision-section, .programs-section {
            padding: 30px 20px;
        }
    }
</style>

<!-- About Ogbonnaya Onu Polytechnic -->
<div class="about-container">
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="hero-content">
            <img src="images/logo.jpg" alt="Ogbonnaya Onu Polytechnic Logo" class="hero-logo" onerror="this.style.display='none';">
            <h1>Ogbonnaya Onu Polytechnic</h1>
            <p>Excellence in Technical Education & Innovation</p>
            <p><i class="fas fa-map-marker-alt"></i> Abakaliki, Ebonyi State, Nigeria</p>
        </div>
    </div>

    <!-- About Section -->
    <div class="content-section">
        <h2 class="section-title">About Our Institution</h2>
        <div class="about-text">
            <p>Ogbonnaya Onu Polytechnic stands as a beacon of excellence in technical and vocational education in Ebonyi State, Nigeria. Named after the distinguished Nigerian politician and technocrat, Dr. Ogbonnaya Onu, our institution embodies his vision of technological advancement and educational excellence.</p>

            <p>Established with the mission to provide world-class technical education, we have consistently maintained our commitment to producing skilled professionals who contribute meaningfully to Nigeria's technological and economic development. Our polytechnic serves as a bridge between theoretical knowledge and practical application, ensuring our graduates are industry-ready and globally competitive.</p>

            <p>Located in the heart of Abakaliki, Ebonyi State, we pride ourselves on our state-of-the-art facilities, experienced faculty, and innovative approach to learning. Our institution has become a preferred destination for students seeking quality technical education in the South-East region of Nigeria.</p>
        </div>
    </div>

    <!-- Mission & Vision Section -->
    <div class="mission-vision-section">
        <h2 class="section-title">Our Mission & Vision</h2>
        <div class="mv-grid">
            <div class="mv-card">
                <i class="fas fa-bullseye"></i>
                <h3>Our Mission</h3>
                <p>To provide excellent technical and vocational education that empowers students with practical skills, innovative thinking, and entrepreneurial spirit needed to excel in the global marketplace while contributing to sustainable national development.</p>
            </div>
            <div class="mv-card">
                <i class="fas fa-eye"></i>
                <h3>Our Vision</h3>
                <p>To be the leading polytechnic in Nigeria, recognized for academic excellence, technological innovation, and the production of skilled professionals who drive economic growth and technological advancement in Africa.</p>
            </div>
            <div class="mv-card">
                <i class="fas fa-heart"></i>
                <h3>Our Values</h3>
                <p>Excellence, Integrity, Innovation, Inclusivity, and Service. We are committed to maintaining the highest standards in education while fostering a culture of continuous learning and ethical leadership.</p>
            </div>
        </div>
    </div>

    <!-- Academic Programs Section -->
    <div class="programs-section">
        <h2 class="section-title">Academic Programs</h2>
        <div class="programs-grid">
            <div class="program-card">
                <h4><i class="fas fa-laptop-code"></i> Computer Science & Technology</h4>
                <p>Comprehensive programs in software development, computer systems, networking, and emerging technologies.</p>
            </div>
            <div class="program-card">
                <h4><i class="fas fa-cogs"></i> Engineering Technology</h4>
                <p>Practical engineering programs covering mechanical, electrical, civil, and electronic engineering technologies.</p>
            </div>
            <div class="program-card">
                <h4><i class="fas fa-calculator"></i> Applied Sciences</h4>
                <p>Mathematics, Physics, Chemistry, and Statistics programs with strong practical applications.</p>
            </div>
            <div class="program-card">
                <h4><i class="fas fa-chart-line"></i> Business Studies</h4>
                <p>Accounting, Business Administration, Marketing, and Entrepreneurship programs for business leaders.</p>
            </div>
            <div class="program-card">
                <h4><i class="fas fa-seedling"></i> Agricultural Technology</h4>
                <p>Modern agricultural practices, agribusiness, and sustainable farming technologies.</p>
            </div>
            <div class="program-card">
                <h4><i class="fas fa-paint-brush"></i> Art & Design</h4>
                <p>Creative programs in graphic design, fine arts, and multimedia production.</p>
            </div>
        </div>
    </div>

    <!-- Statistics Section -->
    <div class="stats-section">
        <h2 class="section-title" style="color: white;">Our Impact</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <h3>5000+</h3>
                <p>Graduates</p>
            </div>
            <div class="stat-item">
                <h3>2500+</h3>
                <p>Current Students</p>
            </div>
            <div class="stat-item">
                <h3>150+</h3>
                <p>Expert Faculty</p>
            </div>
            <div class="stat-item">
                <h3>25+</h3>
                <p>Academic Programs</p>
            </div>
        </div>
    </div>

    <!-- Back Button -->
    <div class="back-button-container">
        <a href="index.php" class="back-button">
            <i class="fas fa-home"></i> Back to Home
        </a>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
