/* Main Styles for Ogbonnaya Onu E-Voting System */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

.navbar-brand img {
    border-radius: 50%;
}

.bg-primary {
    background-color: #007bff !important;
}

.text-primary {
    color: #007bff !important;
}

.progress-bar {
    background-color: #007bff !important;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: transform 0.3s;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background-color: #007bff;
    color: white;
    border-radius: 10px 10px 0 0 !important;
    font-weight: bold;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.candidate-card {
    text-align: center;
    padding: 15px;
}

.candidate-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 auto 15px;
    border: 3px solid #007bff;
}

.position-title {
    background-color: #f8f9fa;
    padding: 10px;
    margin-bottom: 20px;
    border-left: 5px solid #007bff;
    font-weight: bold;
}

.vote-option {
    margin-bottom: 10px;
    background-color: #f0f0f0;
    padding: 8px;
    border-radius: 5px;
    text-align: center;
}

.vote-option input[type="radio"] {
    margin-right: 5px;
}

.vote-option label {
    font-weight: bold;
    color: #007bff;
    cursor: pointer;
}

.results-container {
    margin-top: 30px;
}

.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 30px;
}

.login-container, .register-container {
    max-width: 500px;
    margin: 50px auto;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    text-align: center;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    text-align: center;
}

.home-banner {
    background-image: url('../images/image1.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    text-align: center;
    padding: 100px 0;
    margin-bottom: 30px;
    position: relative;
}

.home-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
}

.home-banner-content {
    position: relative;
    z-index: 1;
}

.home-banner h1 {
    font-size: 3rem;
    margin-bottom: 20px;
}

.home-banner p {
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto 30px;
}

/* Admin styles */
.admin-sidebar {
    background-color: #343a40;
    color: white;
    min-height: calc(100vh - 56px);
    padding-top: 20px;
}

.admin-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 10px 20px;
    margin-bottom: 5px;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.admin-sidebar .nav-link i {
    margin-right: 10px;
}

.admin-content {
    padding: 20px;
}

/* Enhanced Dashboard Cards */
.dashboard-card {
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0;
    background-color: white;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

/* Compact table styling */
.table-sm th,
.table-sm td {
    padding: 0.5rem;
    vertical-align: middle;
}

/* Card header improvements */
.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Sidebar improvements */
.sidebar {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    background-color: #f8f9fc;
}

.sidebar .nav-link {
    color: #858796;
    padding: 0.75rem 1rem;
    position: relative;
}

.sidebar .nav-link:hover {
    color: #5a5c69;
    background-color: #eaecf4;
}

.sidebar .nav-link.active {
    color: #6e707e;
    background-color: #e3e6f0;
    font-weight: 700;
}

.sidebar .nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #007bff;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .col-lg-3.col-md-6 {
        margin-bottom: 1rem;
    }
}

/* Real-time updates animation */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

.real-time-update {
    animation: pulse 2s infinite;
}

/* About page styles */
.skills-container {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.skills-container .badge {
    font-size: 1rem;
    padding: 8px 15px;
    border-radius: 25px;
    margin-right: 5px;
    margin-bottom: 8px;
    font-weight: 500;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.skills-container .badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Skill-specific colors */
.badge-web-dev {
    background-color: #4285F4;
    color: white;
}

.badge-mobile-dev {
    background-color: #34A853;
    color: white;
}

.badge-data {
    background-color: #FBBC05;
    color: #333;
}

.badge-database {
    background-color: #EA4335;
    color: white;
}

.badge-design {
    background-color: #9C27B0;
    color: white;
}

.badge-art {
    background-color: #FF9800;
    color: white;
}

.badge-programming {
    background-color: #3F51B5;
    color: white;
}

/* Badge pulse animation for click effect */
@keyframes badgePulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
}

.badge-pulse {
    animation: badgePulse 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Contact page styles */
.contact-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 15px;
    flex-shrink: 0;
    font-size: 24px;
}





.social-icons {
    display: flex;
    justify-content: flex-start;
    gap: 30px;
    margin-bottom: 20px;
    padding: 15px 0;
}

.social-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 32px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-icon:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Social media brand colors */
.social-icon.facebook {
    background-color: #3b5998;
}

.social-icon.twitter {
    background-color: #1da1f2;
}

.social-icon.instagram {
    background: linear-gradient(45deg, #405de6, #5851db, #833ab4, #c13584, #e1306c, #fd1d1d);
}

.social-icon.linkedin {
    background-color: #0077b5;
}

/* Footer styles */
footer {
    margin-top: auto;
}

footer .list-inline-item:not(:last-child) {
    margin-right: 1rem;
}

footer .list-inline-item a {
    transition: color 0.3s;
}

footer .list-inline-item a:hover {
    color: #007bff !important;
    text-decoration: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .home-banner h1 {
        font-size: 2rem;
    }

    .home-banner p {
        font-size: 1rem;
    }

    .candidate-photo {
        width: 120px;
        height: 120px;
    }

    .contact-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .social-icons {
        justify-content: center;
    }

    .social-icon {
        width: 65px;
        height: 65px;
        font-size: 1.8rem;
    }

    .card-body {
        padding: 1rem;
    }
}
