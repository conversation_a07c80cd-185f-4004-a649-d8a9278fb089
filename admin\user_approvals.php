<?php
// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection
$conn = require_once '../database/db_config.php';

// Check if approval_status column exists
try {
    $column_check = $conn->query("SHOW COLUMNS FROM students LIKE 'approval_status'");
    if ($column_check && $column_check->num_rows == 0) {
        // Add the column if it doesn't exist
        $conn->query("ALTER TABLE students ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending' AFTER has_voted");
        $conn->query("UPDATE students SET approval_status = 'approved'");

        echo '<div class="alert alert-success">Added approval_status column to students table and set existing users to approved.</div>';
    }
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error checking database structure: ' . $e->getMessage() . '</div>';
}

// Handle approval/rejection actions
if (isset($_POST['action']) && isset($_POST['student_id'])) {
    $student_id = intval($_POST['student_id']);
    $action = $_POST['action'];

    if ($action === 'approve') {
        $stmt = $conn->prepare("UPDATE students SET approval_status = 'approved' WHERE id = ?");
        $stmt->bind_param("i", $student_id);
        if ($stmt->execute()) {
            $success_message = "User approved successfully.";
        } else {
            $error_message = "Failed to approve user: " . $conn->error;
        }
    } elseif ($action === 'reject') {
        $stmt = $conn->prepare("UPDATE students SET approval_status = 'rejected' WHERE id = ?");
        $stmt->bind_param("i", $student_id);
        if ($stmt->execute()) {
            $success_message = "User rejected successfully.";
        } else {
            $error_message = "Failed to reject user: " . $conn->error;
        }
    } elseif ($action === 'delete') {
        // First check if the user has voted
        $stmt = $conn->prepare("SELECT has_voted FROM students WHERE id = ?");
        $stmt->bind_param("i", $student_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $student = $result->fetch_assoc();

        if ($student && $student['has_voted']) {
            $error_message = "Cannot delete a user who has already voted.";
        } else {
            // Start a transaction
            $conn->begin_transaction();

            try {
                // First delete any password reset requests for this student
                $stmt = $conn->prepare("DELETE FROM password_reset_requests WHERE student_id = ?");
                $stmt->bind_param("i", $student_id);
                $stmt->execute();

                // Then delete any votes cast by this student
                $stmt = $conn->prepare("DELETE FROM votes WHERE student_id = ?");
                $stmt->bind_param("i", $student_id);
                $stmt->execute();

                // Finally delete the student
                $stmt = $conn->prepare("DELETE FROM students WHERE id = ?");
                $stmt->bind_param("i", $student_id);
                $stmt->execute();

                // Commit the transaction
                $conn->commit();
                $success_message = "User deleted successfully.";
            } catch (Exception $e) {
                // Rollback the transaction on error
                $conn->rollback();
                $error_message = "Failed to delete user: " . $e->getMessage();
            }
        }
    }
}

// Handle search and filter
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'pending';

// Prepare query based on search and filter
$query = "SELECT id, full_name, matric_no, department, level, approval_status, created_at FROM students";
$where_clauses = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_clauses[] = "(full_name LIKE ? OR matric_no LIKE ? OR department LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'sss';
}

if ($filter !== 'all') {
    $where_clauses[] = "approval_status = ?";
    $params[] = $filter;
    $param_types .= 's';
}

if (!empty($where_clauses)) {
    $query .= " WHERE " . implode(' AND ', $where_clauses);
}

$query .= " ORDER BY created_at DESC";

// Prepare and execute the query
try {
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    $students = $result->fetch_all(MYSQLI_ASSOC);
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error retrieving student data: ' . $e->getMessage() . '</div>';
    $students = [];
}

// Get counts for each status
$status_counts = [];
try {
    $stmt = $conn->prepare("SELECT approval_status, COUNT(*) as count FROM students GROUP BY approval_status");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $status_counts[$row['approval_status']] = $row['count'];
    }
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error retrieving status counts: ' . $e->getMessage() . '</div>';
}

// Set default counts if not present
if (!isset($status_counts['pending'])) $status_counts['pending'] = 0;
if (!isset($status_counts['approved'])) $status_counts['approved'] = 0;
if (!isset($status_counts['rejected'])) $status_counts['rejected'] = 0;

// Include admin header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">User Approvals</h1>
            </div>

            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Status cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-white bg-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="card-title">Pending Approvals</h5>
                                    <h2 class="mb-0"><?php echo $status_counts['pending']; ?></h2>
                                </div>
                                <i class="fas fa-clock fa-3x"></i>
                            </div>
                            <a href="?filter=pending" class="text-white">View all pending</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-white bg-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="card-title">Approved Users</h5>
                                    <h2 class="mb-0"><?php echo $status_counts['approved']; ?></h2>
                                </div>
                                <i class="fas fa-check-circle fa-3x"></i>
                            </div>
                            <a href="?filter=approved" class="text-white">View all approved</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-white bg-danger">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="card-title">Rejected Users</h5>
                                    <h2 class="mb-0"><?php echo $status_counts['rejected']; ?></h2>
                                </div>
                                <i class="fas fa-times-circle fa-3x"></i>
                            </div>
                            <a href="?filter=rejected" class="text-white">View all rejected</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and filter form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" action="user_approvals.php" class="form-inline">
                        <div class="form-group mr-2 mb-2">
                            <input type="text" class="form-control" name="search" placeholder="Search by name, matric no, or department" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="form-group mr-2 mb-2">
                            <select class="form-control" name="filter">
                                <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Users</option>
                                <option value="pending" <?php echo $filter === 'pending' ? 'selected' : ''; ?>>Pending Approval</option>
                                <option value="approved" <?php echo $filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                <option value="rejected" <?php echo $filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary mb-2">Search</button>
                        <?php if (!empty($search) || $filter !== 'pending'): ?>
                            <a href="user_approvals.php" class="btn btn-secondary mb-2 ml-2">Clear Filters</a>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <!-- Users list -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <?php
                        if ($filter === 'pending') echo 'Pending Approvals';
                        elseif ($filter === 'approved') echo 'Approved Users';
                        elseif ($filter === 'rejected') echo 'Rejected Users';
                        else echo 'All Users';
                        ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (count($students) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Matric No</th>
                                        <th>Department</th>
                                        <th>Level</th>
                                        <th>Status</th>
                                        <th>Registration Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                                            <td><?php echo htmlspecialchars($student['matric_no']); ?></td>
                                            <td><?php echo htmlspecialchars($student['department']); ?></td>
                                            <td><?php echo htmlspecialchars($student['level']); ?></td>
                                            <td>
                                                <?php if ($student['approval_status'] === 'pending'): ?>
                                                    <span class="badge badge-warning">Pending</span>
                                                <?php elseif ($student['approval_status'] === 'approved'): ?>
                                                    <span class="badge badge-success">Approved</span>
                                                <?php elseif ($student['approval_status'] === 'rejected'): ?>
                                                    <span class="badge badge-danger">Rejected</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($student['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <?php if ($student['approval_status'] !== 'approved'): ?>
                                                        <form method="post" class="d-inline">
                                                            <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                                            <input type="hidden" name="action" value="approve">
                                                            <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Are you sure you want to approve this user?')">
                                                                <i class="fas fa-check"></i> Approve
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>

                                                    <?php if ($student['approval_status'] !== 'rejected'): ?>
                                                        <form method="post" class="d-inline ml-1">
                                                            <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                                            <input type="hidden" name="action" value="reject">
                                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to reject this user?')">
                                                                <i class="fas fa-times"></i> Reject
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>

                                                    <form method="post" class="d-inline ml-1">
                                                        <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                                        <input type="hidden" name="action" value="delete">
                                                        <button type="submit" class="btn btn-sm btn-dark" onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-center">No users found matching your criteria.</p>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
// Include admin footer
include 'includes/footer.php';
?>
