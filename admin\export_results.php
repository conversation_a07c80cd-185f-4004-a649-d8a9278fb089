<?php
// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection
$conn = require_once '../database/db_config.php';

// Set headers for CSV download
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="election_results_' . date('Y-m-d') . '.csv"');

// Create output stream
$output = fopen('php://output', 'w');

// Add BOM for Excel to recognize UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Add header row
fputcsv($output, ['Position', 'Candidate', 'Program', 'Votes', 'Percentage']);

// Get positions and candidates with vote counts
$sql = "SELECT id, position_name FROM positions ORDER BY id";
$positions_result = $conn->query($sql);

while ($position = $positions_result->fetch_assoc()) {
    $position_id = $position['id'];
    $position_name = $position['position_name'];
    
    // Get candidates for this position
    $sql = "SELECT c.id, c.full_name, c.program, COUNT(v.id) as votes
            FROM candidates c
            LEFT JOIN votes v ON c.id = v.candidate_id
            WHERE c.position_id = $position_id
            GROUP BY c.id
            ORDER BY votes DESC";
    
    $candidates_result = $conn->query($sql);
    
    // Calculate total votes for this position
    $total_position_votes = 0;
    $candidates = [];
    
    while ($candidate = $candidates_result->fetch_assoc()) {
        $total_position_votes += $candidate['votes'];
        $candidates[] = $candidate;
    }
    
    // Add position header
    fputcsv($output, [$position_name, '', '', '', '']);
    
    // Add candidates
    foreach ($candidates as $candidate) {
        $percentage = $total_position_votes > 0 ? round(($candidate['votes'] / $total_position_votes) * 100, 2) : 0;
        fputcsv($output, [
            '',
            $candidate['full_name'],
            $candidate['program'],
            $candidate['votes'],
            $percentage . '%'
        ]);
    }
    
    // Add empty row between positions
    fputcsv($output, ['', '', '', '', '']);
}

// Add summary statistics
fputcsv($output, ['Summary Statistics', '', '', '', '']);

// Total registered students
$sql = "SELECT COUNT(*) as total FROM students";
$result = $conn->query($sql);
$total_students = $result->fetch_assoc()['total'];
fputcsv($output, ['Total Registered Students', $total_students, '', '', '']);

// Total votes cast
$sql = "SELECT COUNT(DISTINCT student_id) as total FROM votes";
$result = $conn->query($sql);
$total_votes = $result->fetch_assoc()['total'];
fputcsv($output, ['Total Votes Cast', $total_votes, '', '', '']);

// Voter turnout
$voter_turnout = $total_students > 0 ? round(($total_votes / $total_students) * 100, 2) : 0;
fputcsv($output, ['Voter Turnout', $voter_turnout . '%', '', '', '']);

// Votes by level
fputcsv($output, ['', '', '', '', '']);
fputcsv($output, ['Votes by Level', '', '', '', '']);
fputcsv($output, ['Level', 'Number of Votes', 'Percentage', '', '']);

$sql = "SELECT s.level, COUNT(DISTINCT s.id) as total
        FROM students s
        JOIN votes v ON s.id = v.student_id
        GROUP BY s.level
        ORDER BY s.level";
$result = $conn->query($sql);

while ($row = $result->fetch_assoc()) {
    $level_percentage = $total_votes > 0 ? round(($row['total'] / $total_votes) * 100, 2) : 0;
    fputcsv($output, [
        $row['level'],
        $row['total'],
        $level_percentage . '%',
        '',
        ''
    ]);
}

// Close the output stream
fclose($output);
exit;
?>
