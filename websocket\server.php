<?php
// This is a simple WebSocket server for real-time vote updates
// In a production environment, you would use a more robust solution like <PERSON><PERSON> or Swoole

// Set time limit to zero for long running process
set_time_limit(0);

// Include database configuration
$conn = require_once '../database/db_config.php';

// WebSocket server settings
$host = '0.0.0.0'; // Listen on all available network interfaces
$port = 8080;

// Create WebSocket
$socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
socket_set_option($socket, SOL_SOCKET, SO_REUSEADDR, 1);

// Bind socket to address and port
if (!socket_bind($socket, $host, $port)) {
    echo "Could not bind to socket: " . socket_strerror(socket_last_error()) . "\n";
    exit;
}

// Start listening for connections
if (!socket_listen($socket)) {
    echo "Could not set up socket listener: " . socket_strerror(socket_last_error()) . "\n";
    exit;
}

echo "WebSocket server started on $host:$port\n";

// Array to store client connections
$clients = [];
$last_results = [];

// Function to send data to all connected clients
function broadcast($message) {
    global $clients;
    
    foreach ($clients as $client) {
        // Prepare WebSocket frame
        $response = chr(129) . chr(strlen($message)) . $message;
        socket_write($client, $response, strlen($response));
    }
}

// Function to fetch current vote results
function getVoteResults($conn) {
    $results = [];
    
    // Get all positions
    $sql = "SELECT id, position_name FROM positions ORDER BY id";
    $positions_result = $conn->query($sql);
    
    while ($position = $positions_result->fetch_assoc()) {
        $position_id = $position['id'];
        $position_data = [
            'position_id' => $position_id,
            'position_name' => $position['position_name'],
            'candidates' => []
        ];
        
        // Get candidates and vote counts for this position
        $sql = "SELECT c.id, c.full_name as name, c.photo, c.program, 
                COUNT(v.id) as votes
                FROM candidates c
                LEFT JOIN votes v ON c.id = v.candidate_id
                WHERE c.position_id = $position_id
                GROUP BY c.id
                ORDER BY votes DESC";
        
        $candidates_result = $conn->query($sql);
        
        while ($candidate = $candidates_result->fetch_assoc()) {
            $position_data['candidates'][] = [
                'id' => $candidate['id'],
                'name' => $candidate['name'],
                'photo' => $candidate['photo'],
                'program' => $candidate['program'],
                'votes' => (int)$candidate['votes']
            ];
        }
        
        $results[] = $position_data;
    }
    
    return $results;
}

// Main loop to handle connections
while (true) {
    // Accept new connections
    $new_socket = socket_accept($socket);
    if ($new_socket !== false) {
        // Perform WebSocket handshake
        $headers = socket_read($new_socket, 1024);
        if (preg_match('/Sec-WebSocket-Key: (.*)\r\n/', $headers, $matches)) {
            $key = $matches[1];
            $accept = base64_encode(sha1($key . '258EAFA5-E914-47DA-95CA-C5AB0DC85B11', true));
            
            $headers = "HTTP/1.1 101 Switching Protocols\r\n";
            $headers .= "Upgrade: websocket\r\n";
            $headers .= "Connection: Upgrade\r\n";
            $headers .= "Sec-WebSocket-Accept: $accept\r\n\r\n";
            
            socket_write($new_socket, $headers, strlen($headers));
            
            // Add client to the list
            $clients[] = $new_socket;
            
            echo "New client connected\n";
        }
    }
    
    // Check for new vote data every 2 seconds
    $current_results = getVoteResults($conn);
    
    // Compare with last results
    $results_changed = json_encode($current_results) !== json_encode($last_results);
    
    if ($results_changed) {
        // Send updates to all clients
        $message = json_encode([
            'type' => 'vote_update',
            'results' => $current_results
        ]);
        
        broadcast($message);
        $last_results = $current_results;
    }
    
    // Sleep to reduce CPU usage
    usleep(2000000); // 2 seconds
}

// Close the socket
socket_close($socket);
?>
