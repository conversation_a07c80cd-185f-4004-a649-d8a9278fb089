# E-Voting System Deployment Launcher
# Ogbonnaya Onu Polytechnic
# PowerShell Version

param(
    [string]$XamppPath = "C:\xampp",
    [string]$ProjectName = "VOTE"
)

# Set console properties
$Host.UI.RawUI.WindowTitle = "E-Voting System Deployment"
$Host.UI.RawUI.BackgroundColor = "DarkBlue"
$Host.UI.RawUI.ForegroundColor = "White"
Clear-Host

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to write status messages
function Write-Status {
    param(
        [string]$Message,
        [string]$Type = "INFO"
    )
    
    switch ($Type) {
        "SUCCESS" { Write-ColorOutput "✓ [SUCCESS] $Message" "Green" }
        "ERROR"   { Write-ColorOutput "✗ [ERROR] $Message" "Red" }
        "WARNING" { Write-ColorOutput "⚠ [WARNING] $Message" "Yellow" }
        "INFO"    { Write-ColorOutput "ℹ [INFO] $Message" "Cyan" }
        default   { Write-ColorOutput $Message }
    }
}

# Header
Write-ColorOutput "========================================" "Magenta"
Write-ColorOutput "   E-VOTING SYSTEM DEPLOYMENT LAUNCHER" "Magenta"
Write-ColorOutput "   Ogbonnaya Onu Polytechnic" "Magenta"
Write-ColorOutput "========================================" "Magenta"
Write-Host ""

# Check execution policy
$executionPolicy = Get-ExecutionPolicy
if ($executionPolicy -eq "Restricted") {
    Write-Status "PowerShell execution policy is restricted" "WARNING"
    Write-Status "You may need to run: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" "INFO"
    Write-Host ""
}

# Get script directory
$ScriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectPath = $ScriptPath

Write-Status "Project location: $ProjectPath" "INFO"
Write-Status "Looking for XAMPP at: $XamppPath" "INFO"
Write-Host ""

# Check if XAMPP exists
if (-not (Test-Path $XamppPath)) {
    Write-Status "XAMPP not found at $XamppPath" "ERROR"
    Write-Status "Please install XAMPP or update the XamppPath parameter" "INFO"
    Write-Status "Download XAMPP from: https://www.apachefriends.org/" "INFO"
    Read-Host "Press Enter to exit"
    exit 1
}

# Check XAMPP components
$ApachePath = Join-Path $XamppPath "apache\bin\httpd.exe"
$MysqlPath = Join-Path $XamppPath "mysql\bin\mysqld.exe"

if (-not (Test-Path $ApachePath)) {
    Write-Status "Apache not found in XAMPP installation" "ERROR"
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path $MysqlPath)) {
    Write-Status "MySQL not found in XAMPP installation" "ERROR"
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Status "XAMPP installation verified successfully" "SUCCESS"
Write-Host ""

# Function to check if process is running
function Test-ProcessRunning {
    param([string]$ProcessName)
    return (Get-Process -Name $ProcessName -ErrorAction SilentlyContinue) -ne $null
}

# Check service status
Write-Status "Checking service status..." "INFO"

$ApacheRunning = Test-ProcessRunning "httpd"
$MysqlRunning = Test-ProcessRunning "mysqld"

if ($ApacheRunning) {
    Write-Status "Apache is already running" "INFO"
} else {
    Write-Status "Apache is not running" "INFO"
}

if ($MysqlRunning) {
    Write-Status "MySQL is already running" "INFO"
} else {
    Write-Status "MySQL is not running" "INFO"
}

Write-Host ""

# Start Apache if not running
if (-not $ApacheRunning) {
    Write-Status "Starting Apache web server..." "INFO"
    try {
        Start-Process -FilePath $ApachePath -WindowStyle Hidden
        Start-Sleep -Seconds 3
        
        if (Test-ProcessRunning "httpd") {
            Write-Status "Apache started successfully" "SUCCESS"
        } else {
            Write-Status "Failed to start Apache" "ERROR"
            Read-Host "Press Enter to exit"
            exit 1
        }
    } catch {
        Write-Status "Error starting Apache: $($_.Exception.Message)" "ERROR"
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Status "Apache already running, skipping start" "INFO"
}

# Start MySQL if not running
if (-not $MysqlRunning) {
    Write-Status "Starting MySQL database server..." "INFO"
    try {
        $MysqlConfigPath = Join-Path $XamppPath "mysql\bin\my.ini"
        Start-Process -FilePath $MysqlPath -ArgumentList "--defaults-file=`"$MysqlConfigPath`"" -WindowStyle Hidden
        Start-Sleep -Seconds 5
        
        if (Test-ProcessRunning "mysqld") {
            Write-Status "MySQL started successfully" "SUCCESS"
        } else {
            Write-Status "Failed to start MySQL" "ERROR"
            Read-Host "Press Enter to exit"
            exit 1
        }
    } catch {
        Write-Status "Error starting MySQL: $($_.Exception.Message)" "ERROR"
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Status "MySQL already running, skipping start" "INFO"
}

Write-Host ""
Write-Status "Waiting for services to fully initialize..." "INFO"
Start-Sleep -Seconds 3

# Setup project in htdocs
$HtdocsProject = Join-Path $XamppPath "htdocs\$ProjectName"

if (-not (Test-Path $HtdocsProject)) {
    Write-Status "Project not found in htdocs, copying files..." "INFO"
    try {
        New-Item -ItemType Directory -Path $HtdocsProject -Force | Out-Null
        
        # Copy files (excluding PowerShell and batch files)
        Get-ChildItem -Path $ProjectPath -File | Where-Object { 
            $_.Extension -notin @('.ps1', '.bat') 
        } | Copy-Item -Destination $HtdocsProject -Force
        
        # Copy directories
        Get-ChildItem -Path $ProjectPath -Directory | Copy-Item -Destination $HtdocsProject -Recurse -Force
        
        Write-Status "Project files copied to htdocs" "SUCCESS"
    } catch {
        Write-Status "Error copying files: $($_.Exception.Message)" "ERROR"
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Status "Project already exists in htdocs" "INFO"
    Write-Status "Updating files..." "INFO"
    
    try {
        # Update files (excluding PowerShell and batch files)
        Get-ChildItem -Path $ProjectPath -File | Where-Object { 
            $_.Extension -notin @('.ps1', '.bat') 
        } | Copy-Item -Destination $HtdocsProject -Force
        
        Write-Status "Project files updated" "SUCCESS"
    } catch {
        Write-Status "Error updating files: $($_.Exception.Message)" "WARNING"
    }
}

Write-Host ""
Write-Status "Testing web server connection..." "INFO"

# Test localhost connectivity
try {
    $TestConnection = Test-NetConnection -ComputerName "127.0.0.1" -Port 80 -InformationLevel Quiet -WarningAction SilentlyContinue
    if ($TestConnection) {
        Write-Status "Localhost connection available" "SUCCESS"
    } else {
        Write-Status "Cannot reach localhost on port 80" "ERROR"
        Read-Host "Press Enter to exit"
        exit 1
    }
} catch {
    Write-Status "Network test failed, but continuing..." "WARNING"
}

# Construct deployment URL
$DeployUrl = "http://localhost/$ProjectName/deploy.php"

Write-Host ""
Write-ColorOutput "========================================" "Magenta"
Write-ColorOutput "   DEPLOYMENT READY!" "Magenta"
Write-ColorOutput "========================================" "Magenta"
Write-Host ""

Write-Status "Services Status:" "INFO"
Write-ColorOutput "  - Apache Web Server: RUNNING" "Green"
Write-ColorOutput "  - MySQL Database: RUNNING" "Green"
Write-ColorOutput "  - Project Location: $HtdocsProject" "White"
Write-Host ""

Write-Status "Opening deployment interface..." "INFO"
Write-Status "URL: $DeployUrl" "INFO"
Write-Host ""

# Open deployment page
try {
    Start-Process $DeployUrl
    Write-Status "Deployment interface launched!" "SUCCESS"
} catch {
    Write-Status "Could not open browser automatically" "WARNING"
    Write-Status "Please manually open: $DeployUrl" "INFO"
}

Write-Host ""
Write-Status "The deployment page should open in your browser." "INFO"
Write-Status "If it doesn't open automatically, copy this URL:" "INFO"
Write-ColorOutput "  $DeployUrl" "Yellow"
Write-Host ""

Write-Status "After successful deployment, you can access:" "INFO"
Write-ColorOutput "  - Student Portal: http://localhost/$ProjectName/" "White"
Write-ColorOutput "  - Admin Panel: http://localhost/$ProjectName/admin/" "White"
Write-Host ""

# Wait for user input
Write-ColorOutput "Press any key to close this window..." "Gray"
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Optional: Ask if user wants to stop services
Write-Host ""
$StopServices = Read-Host "Do you want to stop XAMPP services? (Y/N)"
if ($StopServices -eq "Y" -or $StopServices -eq "y") {
    Write-Status "Stopping services..." "INFO"
    try {
        Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
        Get-Process -Name "mysqld" -ErrorAction SilentlyContinue | Stop-Process -Force
        Write-Status "Services stopped" "SUCCESS"
    } catch {
        Write-Status "Error stopping services: $($_.Exception.Message)" "WARNING"
    }
}

Write-Host ""
Write-Status "Deployment launcher finished. Thank you!" "SUCCESS"
Read-Host "Press Enter to exit"
