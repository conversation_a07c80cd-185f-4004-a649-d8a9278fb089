<?php
// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection
$conn = require_once '../database/db_config.php';

// Get positions and candidates with vote counts
$positions = [];
$sql = "SELECT id, position_name FROM positions ORDER BY id";
$positions_result = $conn->query($sql);

while ($position = $positions_result->fetch_assoc()) {
    $position_id = $position['id'];
    $candidates = [];
    
    $sql = "SELECT c.id, c.full_name, c.program, c.photo, COUNT(v.id) as votes
            FROM candidates c
            LEFT JOIN votes v ON c.id = v.candidate_id
            WHERE c.position_id = $position_id
            GROUP BY c.id
            ORDER BY votes DESC";
    
    $candidates_result = $conn->query($sql);
    
    while ($candidate = $candidates_result->fetch_assoc()) {
        $candidates[] = $candidate;
    }
    
    $positions[] = [
        'id' => $position['id'],
        'name' => $position['position_name'],
        'candidates' => $candidates
    ];
}

// Get voting statistics
// Total registered students
$sql = "SELECT COUNT(*) as total FROM students";
$result = $conn->query($sql);
$total_students = $result->fetch_assoc()['total'];

// Total votes cast
$sql = "SELECT COUNT(DISTINCT student_id) as total FROM votes";
$result = $conn->query($sql);
$total_votes = $result->fetch_assoc()['total'];

// Voter turnout
$voter_turnout = $total_students > 0 ? round(($total_votes / $total_students) * 100) : 0;

// Votes by level
$votes_by_level = [];
$sql = "SELECT s.level, COUNT(DISTINCT s.id) as total
        FROM students s
        JOIN votes v ON s.id = v.student_id
        GROUP BY s.level
        ORDER BY s.level";
$result = $conn->query($sql);

while ($row = $result->fetch_assoc()) {
    $votes_by_level[$row['level']] = $row['total'];
}

// Include admin header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main content -->
        <main class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
            <h1 class="h2 mb-4">Election Results</h1>
            
            <!-- Voting statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card dashboard-card">
                        <i class="fas fa-users"></i>
                        <h3>Registered Students</h3>
                        <p><?php echo $total_students; ?></p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card dashboard-card">
                        <i class="fas fa-vote-yea"></i>
                        <h3>Votes Cast</h3>
                        <p><?php echo $total_votes; ?></p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card dashboard-card">
                        <i class="fas fa-chart-pie"></i>
                        <h3>Voter Turnout</h3>
                        <p><?php echo $voter_turnout; ?>%</p>
                    </div>
                </div>
            </div>
            
            <!-- Votes by level -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Votes by Level</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Level</th>
                                            <th>Number of Votes</th>
                                            <th>Percentage</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (['ND1', 'ND2', 'HND1', 'HND2'] as $level): ?>
                                            <tr>
                                                <td><?php echo $level; ?></td>
                                                <td><?php echo isset($votes_by_level[$level]) ? $votes_by_level[$level] : 0; ?></td>
                                                <td>
                                                    <?php 
                                                    $level_percentage = $total_votes > 0 ? round(((isset($votes_by_level[$level]) ? $votes_by_level[$level] : 0) / $total_votes) * 100) : 0;
                                                    echo $level_percentage . '%';
                                                    ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="chart-container">
                                <canvas id="levelChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Results by position -->
            <?php foreach ($positions as $position): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo $position['name']; ?> Results</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Candidate</th>
                                                <th>Program</th>
                                                <th>Votes</th>
                                                <th>Percentage</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $total_position_votes = array_sum(array_column($position['candidates'], 'votes'));
                                            foreach ($position['candidates'] as $candidate): 
                                            ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <img src="../<?php echo $candidate['photo']; ?>" alt="<?php echo $candidate['full_name']; ?>" class="rounded-circle mr-2" width="40" height="40" style="object-fit: cover;">
                                                            <?php echo $candidate['full_name']; ?>
                                                        </div>
                                                    </td>
                                                    <td><?php echo $candidate['program']; ?></td>
                                                    <td><?php echo $candidate['votes']; ?></td>
                                                    <td>
                                                        <?php 
                                                        $percentage = $total_position_votes > 0 ? round(($candidate['votes'] / $total_position_votes) * 100) : 0;
                                                        echo $percentage . '%';
                                                        ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="chart-container">
                                    <canvas id="chart-<?php echo $position['id']; ?>"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <!-- Export results button -->
            <div class="text-center mb-4">
                <a href="export_results.php" class="btn btn-primary">
                    <i class="fas fa-file-export"></i> Export Results
                </a>
            </div>
        </main>
    </div>
</div>

<script>
// Initialize charts when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Level chart
    new Chart(document.getElementById('levelChart'), {
        type: 'pie',
        data: {
            labels: ['ND1', 'ND2', 'HND1', 'HND2'],
            datasets: [{
                data: [
                    <?php echo isset($votes_by_level['ND1']) ? $votes_by_level['ND1'] : 0; ?>,
                    <?php echo isset($votes_by_level['ND2']) ? $votes_by_level['ND2'] : 0; ?>,
                    <?php echo isset($votes_by_level['HND1']) ? $votes_by_level['HND1'] : 0; ?>,
                    <?php echo isset($votes_by_level['HND2']) ? $votes_by_level['HND2'] : 0; ?>
                ],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(255, 159, 64, 0.7)'
                ],
                borderColor: [
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 159, 64, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                            return `${label}: ${value} votes (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
    
    // Position charts
    <?php foreach ($positions as $position): ?>
        new Chart(document.getElementById('chart-<?php echo $position['id']; ?>'), {
            type: 'bar',
            data: {
                labels: [<?php echo implode(', ', array_map(function($candidate) { return "'" . addslashes($candidate['full_name']) . "'"; }, $position['candidates'])); ?>],
                datasets: [{
                    label: 'Votes',
                    data: [<?php echo implode(', ', array_map(function($candidate) { return $candidate['votes']; }, $position['candidates'])); ?>],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${value} votes (${percentage}%)`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
    <?php endforeach; ?>
});
</script>

<?php
// Include admin footer
include 'includes/footer.php';
?>
