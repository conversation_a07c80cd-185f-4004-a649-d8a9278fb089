// Deployment JavaScript
let currentStep = 1;
let deploymentData = {};

// Start deployment process
function startDeployment() {
    showStep(2);
    createDatabase();
}

// Show specific step
function showStep(stepNumber) {
    // Hide all steps
    document.querySelectorAll('.deploy-step').forEach(step => {
        step.classList.remove('active');
    });
    
    // Update step indicators
    for (let i = 1; i <= 4; i++) {
        const stepElement = document.getElementById(`step${i}`);
        stepElement.classList.remove('active', 'completed');
        
        if (i < stepNumber) {
            stepElement.classList.add('completed');
        } else if (i === stepNumber) {
            stepElement.classList.add('active');
        }
    }
    
    // Show current step
    const steps = {
        1: 'welcome-step',
        2: 'database-step',
        3: 'admin-step',
        4: 'complete-step'
    };
    
    document.getElementById(steps[stepNumber]).classList.add('active');
    currentStep = stepNumber;
}

// Create database
function createDatabase() {
    const spinner = document.getElementById('db-spinner');
    const progress = document.getElementById('db-progress');
    const status = document.getElementById('db-status');
    
    spinner.style.display = 'block';
    
    // Simulate database creation progress
    let progressValue = 0;
    const progressInterval = setInterval(() => {
        progressValue += Math.random() * 15;
        if (progressValue > 90) progressValue = 90;
        progress.style.width = progressValue + '%';
    }, 200);
    
    // Make AJAX request to create database
    fetch('deploy_database.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'create_database' })
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);
        progress.style.width = '100%';
        spinner.style.display = 'none';
        
        if (data.success) {
            status.className = 'status-message alert alert-success show';
            status.innerHTML = '<i class="fas fa-check-circle mr-2"></i>' + data.message;
            
            setTimeout(() => {
                showStep(3);
            }, 2000);
        } else {
            status.className = 'status-message alert alert-danger show';
            status.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i>' + data.message;
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        spinner.style.display = 'none';
        status.className = 'status-message alert alert-danger show';
        status.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i>Error: ' + error.message;
    });
}

// Password strength checker
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = [];
    
    // Length check
    if (password.length >= 8) {
        strength += 25;
    } else {
        feedback.push('At least 8 characters');
    }
    
    // Uppercase check
    if (password.match(/[A-Z]/)) {
        strength += 25;
    } else {
        feedback.push('One uppercase letter');
    }
    
    // Lowercase check
    if (password.match(/[a-z]/)) {
        strength += 25;
    } else {
        feedback.push('One lowercase letter');
    }
    
    // Number or special character check
    if (password.match(/[\d\W]/)) {
        strength += 25;
    } else {
        feedback.push('One number or special character');
    }
    
    return { strength, feedback };
}

// Update password strength indicator
function updatePasswordStrength() {
    const password = document.getElementById('admin-password').value;
    const strengthText = document.getElementById('strength-text');
    const strengthFill = document.getElementById('strength-fill');
    
    if (!password) {
        strengthText.textContent = 'Not entered';
        strengthFill.style.width = '0%';
        strengthFill.style.backgroundColor = '#e9ecef';
        return;
    }
    
    const { strength } = checkPasswordStrength(password);
    
    let text, color;
    if (strength < 25) {
        text = 'Very Weak';
        color = '#dc3545';
    } else if (strength < 50) {
        text = 'Weak';
        color = '#fd7e14';
    } else if (strength < 75) {
        text = 'Medium';
        color = '#ffc107';
    } else if (strength < 100) {
        text = 'Strong';
        color = '#20c997';
    } else {
        text = 'Very Strong';
        color = '#28a745';
    }
    
    strengthText.textContent = text;
    strengthFill.style.width = strength + '%';
    strengthFill.style.backgroundColor = color;
}

// Check password match
function checkPasswordMatch() {
    const password = document.getElementById('admin-password').value;
    const confirm = document.getElementById('admin-confirm').value;
    const matchElement = document.getElementById('password-match');
    
    if (!confirm) {
        matchElement.textContent = '';
        return false;
    }
    
    if (password === confirm) {
        matchElement.textContent = '✓ Passwords match';
        matchElement.className = 'form-text text-success';
        return true;
    } else {
        matchElement.textContent = '✗ Passwords do not match';
        matchElement.className = 'form-text text-danger';
        return false;
    }
}

// Create admin account
function createAdminAccount(formData) {
    const submitBtn = document.querySelector('#admin-form button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Account...';
    
    fetch('deploy_admin.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'create_admin',
            username: formData.get('username'),
            password: formData.get('password')
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            deploymentData.adminCreated = true;
            deploymentData.adminUsername = formData.get('username');
            showStep(4);
        } else {
            alert('Error creating admin account: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Password strength monitoring
    document.getElementById('admin-password').addEventListener('input', updatePasswordStrength);
    
    // Password match monitoring
    document.getElementById('admin-confirm').addEventListener('input', checkPasswordMatch);
    
    // Admin form submission
    document.getElementById('admin-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const password = formData.get('password');
        const confirm = formData.get('confirm');
        const username = formData.get('username');
        
        // Validation
        if (!username || username.length < 3) {
            alert('Username must be at least 3 characters long');
            return;
        }
        
        if (!password || password.length < 6) {
            alert('Password must be at least 6 characters long');
            return;
        }
        
        if (password !== confirm) {
            alert('Passwords do not match');
            return;
        }
        
        const { strength } = checkPasswordStrength(password);
        if (strength < 50) {
            if (!confirm('Your password is weak. Continue anyway?')) {
                return;
            }
        }
        
        createAdminAccount(formData);
    });
    
    // Prevent form submission on Enter key in password fields
    document.querySelectorAll('input[type="password"]').forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const form = this.closest('form');
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn && !submitBtn.disabled) {
                    submitBtn.click();
                }
            }
        });
    });
});

// Utility function to show loading state
function showLoading(element, text = 'Loading...') {
    element.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>${text}`;
    element.disabled = true;
}

// Utility function to hide loading state
function hideLoading(element, originalText) {
    element.innerHTML = originalText;
    element.disabled = false;
}

// Check if deployment is already completed
function checkDeploymentStatus() {
    fetch('check_deployment.php')
    .then(response => response.json())
    .then(data => {
        if (data.deployed) {
            // If already deployed, show completion step
            showStep(4);
        }
    })
    .catch(error => {
        console.log('Deployment status check failed:', error);
    });
}

// Initialize deployment status check
checkDeploymentStatus();
