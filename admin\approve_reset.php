<?php
// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection and functions
$conn = require_once '../database/db_config.php';
require_once '../includes/functions.php';

// Check if token and action are provided
if (!isset($_GET['token']) || !isset($_GET['action'])) {
    header('Location: dashboard.php');
    exit;
}

$token = $_GET['token'];
$action = $_GET['action'];

// Get the reset request
$request = getPasswordResetRequest($token);

// Check if request exists and is pending
if (!$request || $request['status'] !== 'pending') {
    $_SESSION['error'] = 'Invalid or expired reset token';
    header('Location: dashboard.php');
    exit;
}

// Process the action
if ($action === 'approve') {
    if (updatePasswordResetStatus($token, 'approved')) {
        // Get student details
        $student_id = $request['student_id'];
        $student = getStudentById($student_id);

        if ($student) {
            // Create a notification in the database
            $reset_link = "http://" . $_SERVER['HTTP_HOST'] . dirname(dirname($_SERVER['PHP_SELF'])) . "/complete_reset.php?token=" . $token;

            // Store the notification in a session variable
            $_SESSION['reset_approved'] = [
                'student_id' => $student_id,
                'student_name' => $student['full_name'],
                'matric_no' => $student['matric_no'],
                'reset_link' => $reset_link
            ];

            $_SESSION['message'] = 'Password reset request approved. The student can now reset their password.';
        } else {
            $_SESSION['error'] = 'Student not found';
        }
    } else {
        $_SESSION['error'] = 'Failed to approve password reset request';
    }
} elseif ($action === 'reject') {
    if (updatePasswordResetStatus($token, 'rejected')) {
        $_SESSION['message'] = 'Password reset request rejected.';
    } else {
        $_SESSION['error'] = 'Failed to reject password reset request';
    }
} else {
    $_SESSION['error'] = 'Invalid action';
}

// Redirect back to dashboard
header('Location: dashboard.php');
exit;
?>
