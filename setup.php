<?php
// Database configuration
$host = "localhost";
$username = "root";
$password = "";
$database = "voting_system";

// Check if already deployed
$deploymentStatusFile = 'deployment_status.json';
$isDeployed = false;
$deploymentData = null;

if (file_exists($deploymentStatusFile)) {
    $deploymentData = json_decode(file_get_contents($deploymentStatusFile), true);
    $isDeployed = isset($deploymentData['deployment_completed']) && $deploymentData['deployment_completed'];
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    if ($_POST['action'] === 'create_database') {
        try {
            // Connect to MySQL server without selecting a database
            $conn = new mysqli($host, $username, $password);
            
            if ($conn->connect_error) {
                throw new Exception("Connection failed: " . $conn->connect_error);
            }
            
            // Create database if it doesn't exist
            $sql = "CREATE DATABASE IF NOT EXISTS $database";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating database: " . $conn->error);
            }
            
            // Select the database
            $conn->select_db($database);
            
            // Create Students table
            $sql = "CREATE TABLE IF NOT EXISTS students (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(255) NOT NULL,
                matric_no VARCHAR(50) NOT NULL UNIQUE,
                department VARCHAR(255) NOT NULL,
                level ENUM('ND1', 'ND2', 'HND1', 'HND2') NOT NULL,
                password VARCHAR(255) NOT NULL,
                has_voted BOOLEAN DEFAULT FALSE,
                approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating students table: " . $conn->error);
            }
            
            // Create Positions table
            $sql = "CREATE TABLE IF NOT EXISTS positions (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                position_name VARCHAR(255) NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating positions table: " . $conn->error);
            }
            
            // Insert default positions
            $positions = ["President", "Director of Sports", "Director of Information"];
            foreach ($positions as $position) {
                $sql = "INSERT IGNORE INTO positions (position_name) VALUES ('$position')";
                $conn->query($sql);
            }
            
            // Create Candidates table
            $sql = "CREATE TABLE IF NOT EXISTS candidates (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(255) NOT NULL,
                position_id INT(11) NOT NULL,
                program VARCHAR(255) NOT NULL,
                photo VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (position_id) REFERENCES positions(id)
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating candidates table: " . $conn->error);
            }
            
            // Create Votes table
            $sql = "CREATE TABLE IF NOT EXISTS votes (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                student_id INT(11) NOT NULL,
                candidate_id INT(11) NOT NULL,
                position_id INT(11) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students(id),
                FOREIGN KEY (candidate_id) REFERENCES candidates(id),
                FOREIGN KEY (position_id) REFERENCES positions(id),
                UNIQUE KEY unique_vote (student_id, position_id)
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating votes table: " . $conn->error);
            }
            
            // Create Admin table
            $sql = "CREATE TABLE IF NOT EXISTS admins (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating admins table: " . $conn->error);
            }
            
            // Create Election Settings table
            $sql = "CREATE TABLE IF NOT EXISTS election_settings (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(50) NOT NULL UNIQUE,
                setting_value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating election_settings table: " . $conn->error);
            }
            
            // Insert default settings
            $default_settings = [
                ['election_status', 'inactive'],
                ['election_title', 'Student Union Government Elections'],
                ['election_start_date', date('Y-m-d')],
                ['election_end_date', date('Y-m-d', strtotime('+1 week'))]
            ];
            
            foreach ($default_settings as $setting) {
                $key = $setting[0];
                $value = $setting[1];
                $sql = "INSERT IGNORE INTO election_settings (setting_key, setting_value) VALUES ('$key', '$value')";
                $conn->query($sql);
            }
            
            // Create Password Reset Requests table
            $sql = "CREATE TABLE IF NOT EXISTS password_reset_requests (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                student_id INT(11) NOT NULL,
                token VARCHAR(255) NOT NULL,
                status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students(id),
                UNIQUE KEY (student_id, status)
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating password_reset_requests table: " . $conn->error);
            }
            
            $conn->close();
            
            // Update deployment status
            $deploymentStatus = [
                'database_created' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'version' => '1.0.0'
            ];
            file_put_contents($deploymentStatusFile, json_encode($deploymentStatus, JSON_PRETTY_PRINT));
            
            echo json_encode(['success' => true, 'message' => 'Database created successfully!']);
            
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }
    
    if ($_POST['action'] === 'create_admin') {
        try {
            $admin_username = trim($_POST['username']);
            $admin_password = $_POST['password'];
            
            if (empty($admin_username) || strlen($admin_username) < 3) {
                throw new Exception('Username must be at least 3 characters long');
            }
            
            if (empty($admin_password) || strlen($admin_password) < 6) {
                throw new Exception('Password must be at least 6 characters long');
            }
            
            // Connect to database
            $conn = new mysqli($host, $username, $password, $database);
            
            if ($conn->connect_error) {
                throw new Exception("Connection failed: " . $conn->connect_error);
            }
            
            // Hash password and create admin
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO admins (username, password) VALUES (?, ?) ON DUPLICATE KEY UPDATE password = ?");
            $stmt->bind_param("sss", $admin_username, $hashed_password, $hashed_password);
            
            if (!$stmt->execute()) {
                throw new Exception("Failed to create admin account");
            }
            
            $conn->close();
            
            // Update deployment status
            $deploymentStatus = json_decode(file_get_contents($deploymentStatusFile), true);
            $deploymentStatus['admin_created'] = true;
            $deploymentStatus['admin_username'] = $admin_username;
            $deploymentStatus['deployment_completed'] = true;
            $deploymentStatus['completed_at'] = date('Y-m-d H:i:s');
            file_put_contents($deploymentStatusFile, json_encode($deploymentStatus, JSON_PRETTY_PRINT));
            
            echo json_encode(['success' => true, 'message' => 'Admin account created successfully!']);
            
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup E-Voting System - Ogbonnaya Onu Polytechnic</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
            line-height: 1.6;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: 1;
        }

        .deploy-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            position: relative;
            z-index: 2;
        }

        .deployment-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2);
            overflow: hidden;
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .deploy-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 50px 30px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .deploy-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .logo-container {
            margin-bottom: 20px;
            position: relative;
            z-index: 3;
        }

        .logo-container img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 4px solid rgba(255,255,255,0.3);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .logo-container img:hover {
            transform: scale(1.1);
        }

        .deploy-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
            z-index: 3;
        }

        .deploy-header .subtitle {
            font-size: 1.1rem;
            color: rgba(255,255,255,0.9);
            font-weight: 400;
            margin-bottom: 8px;
            position: relative;
            z-index: 3;
        }

        .deploy-header .institution {
            font-size: 1rem;
            color: rgba(255,255,255,0.95);
            font-weight: 600;
            background: rgba(255,255,255,0.2);
            padding: 8px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-top: 10px;
            position: relative;
            z-index: 3;
        }

        .deploy-content {
            padding: 40px;
            background: white;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(240, 147, 251, 0.3);
            position: relative;
            overflow: hidden;
        }

        .step-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 25px;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .step-number {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            color: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: 12px;
            transition: all 0.4s ease;
            border: 3px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }

        .step-number.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transform: scale(1.1);
        }

        .step-number.completed {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border-color: white;
            box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
        }

        .step-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: rgba(255,255,255,0.9);
            margin: 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .step-label.active {
            color: white;
            font-weight: 700;
        }

        .step-label.completed {
            color: white;
        }

        .step-arrow {
            color: rgba(255,255,255,0.6);
            margin: 0 15px;
            font-size: 1.2rem;
            align-self: flex-start;
            margin-top: 25px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .deploy-step {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .deploy-step.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            padding: 30px;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        }

        .feature-card:nth-child(2)::before {
            background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%);
        }

        .feature-card:hover {
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transform: translateY(-8px);
            border-color: rgba(255,255,255,0.5);
        }

        .feature-card h4 {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .feature-card h4 i {
            margin-right: 12px;
            font-size: 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .feature-card:nth-child(2) h4 i {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            padding: 10px 0;
            position: relative;
            padding-left: 30px;
            color: #495057;
            font-size: 0.95rem;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .feature-list li:hover {
            color: #2c3e50;
        }

        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            top: 10px;
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.8rem;
        }

        .btn-deploy {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 15px 35px;
            font-size: 1.1rem;
            font-weight: 700;
            border-radius: 50px;
            color: white;
            cursor: pointer;
            transition: all 0.4s ease;
            text-decoration: none;
            display: inline-block;
            border: 2px solid transparent;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-deploy::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-deploy:hover::before {
            left: 100%;
        }

        .btn-deploy:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-deploy:disabled {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.2);
        }

        .btn-deploy.btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            box-shadow: 0 8px 25px rgba(17, 153, 142, 0.3);
        }

        .btn-deploy.btn-success:hover {
            background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
            box-shadow: 0 15px 35px rgba(17, 153, 142, 0.4);
        }

        .progress-container {
            margin: 40px 0;
        }

        .progress {
            height: 12px;
            border-radius: 20px;
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .progress-bar {
            border-radius: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.8s ease;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progressShimmer 2s infinite;
        }

        @keyframes progressShimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .status-message {
            padding: 20px;
            border-radius: 15px;
            margin: 25px 0;
            display: none;
            border-left: 5px solid;
            backdrop-filter: blur(10px);
        }

        .status-message.show {
            display: block;
            animation: slideIn 0.6s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .loading-spinner {
            display: none;
            text-align: center;
            margin: 30px 0;
        }

        .spinner-border {
            width: 4rem;
            height: 4rem;
            border: 4px solid rgba(102, 126, 234, 0.2);
            border-top: 4px solid #667eea;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .admin-form {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
            backdrop-filter: blur(10px);
            padding: 35px;
            border-radius: 20px;
            margin: 30px 0;
            border: 1px solid rgba(255,255,255,0.3);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .form-control {
            border-radius: 15px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            padding: 15px 20px;
            transition: all 0.4s ease;
            background: rgba(255,255,255,0.8);
            backdrop-filter: blur(5px);
            font-size: 1rem;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
            transform: translateY(-2px);
        }

        .password-strength {
            margin-top: 15px;
        }

        .strength-bar {
            height: 8px;
            border-radius: 10px;
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
            margin-top: 8px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .strength-fill {
            height: 100%;
            transition: all 0.5s ease;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }

        .strength-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: strengthShimmer 2s infinite;
        }

        @keyframes strengthShimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .form-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            display: block;
        }

        .form-group label i {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @media (max-width: 768px) {
            .deploy-container {
                margin: 10px;
                padding: 15px;
            }

            .deployment-card {
                border-radius: 15px;
            }

            .deploy-header {
                padding: 30px 20px 25px;
            }

            .deploy-header h1 {
                font-size: 2rem;
            }

            .logo-container img {
                width: 60px;
                height: 60px;
            }

            .deploy-content {
                padding: 25px;
            }

            .step-indicator {
                flex-direction: column;
                padding: 20px;
            }

            .step {
                margin: 10px 0;
            }

            .step-arrow {
                transform: rotate(90deg);
                margin: 8px 0;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 25px;
            }

            .feature-card {
                padding: 25px;
            }

            .admin-form {
                padding: 25px;
            }

            .btn-deploy {
                padding: 12px 25px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="deploy-container">
        <div class="deployment-card">
            <div class="deploy-header">
                <div class="logo-container">
                    <img src="images/logo.jpg" alt="Ogbonnaya Onu Polytechnic Logo">
                </div>
                <h1>🗳️ E-Voting System Setup</h1>
                <p class="subtitle">✨ Automated setup and configuration</p>
                <p class="institution">🎓 Ogbonnaya Onu Polytechnic</p>
            </div>

            <div class="deploy-content">
                <!-- Step Indicator -->
                <?php if (!$isDeployed): ?>
                <div class="step-indicator">
                    <div class="step">
                        <div class="step-number active" id="step1">1</div>
                        <p class="step-label active">Welcome</p>
                    </div>
                    <div class="step-arrow">→</div>
                    <div class="step">
                        <div class="step-number" id="step2">2</div>
                        <p class="step-label">Database</p>
                    </div>
                    <div class="step-arrow">→</div>
                    <div class="step">
                        <div class="step-number" id="step3">3</div>
                        <p class="step-label">Admin</p>
                    </div>
                    <div class="step-arrow">→</div>
                    <div class="step">
                        <div class="step-number" id="step4">4</div>
                        <p class="step-label">Complete</p>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Step 1: Welcome -->
                <div class="deploy-step <?php echo $isDeployed ? '' : 'active'; ?>" id="welcome-step">
                    <div style="text-align: center; margin-bottom: 40px;">
                        <h2 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; margin-bottom: 15px; font-size: 2.2rem;">🚀 Welcome to E-Voting System Setup</h2>
                        <p style="color: #6c757d; font-size: 1.1rem; max-width: 600px; margin: 0 auto; line-height: 1.6;">This automated setup wizard will configure your complete e-voting system with database, admin account, and all necessary components in just a few simple steps!</p>
                    </div>

                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4><i class="fas fa-magic"></i>✨ What will be installed</h4>
                            <ul class="feature-list">
                                <li>🗄️ Complete database structure</li>
                                <li>👥 Student management system</li>
                                <li>🏆 Candidate registration</li>
                                <li>🗳️ Secure voting mechanism</li>
                                <li>⚙️ Admin control panel</li>
                                <li>📊 Election settings & analytics</li>
                                <li>🔒 Advanced security features</li>
                            </ul>
                        </div>
                        <div class="feature-card">
                            <h4><i class="fas fa-clipboard-check"></i>📋 System Requirements</h4>
                            <ul class="feature-list">
                                <li>🖥️ XAMPP/WAMP running</li>
                                <li>🗃️ MySQL server active</li>
                                <li>⚡ PHP 7.4 or higher</li>
                                <li>🌐 Modern web browser</li>
                                <li>💾 500MB free disk space</li>
                                <li>🔗 Internet connection</li>
                            </ul>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 40px;">
                        <p style="color: #6c757d; margin-bottom: 20px; font-size: 1rem;">Ready to set up your professional e-voting system?</p>
                        <button class="btn-deploy btn-success" onclick="startDeployment()">
                            <i class="fas fa-rocket" style="margin-right: 10px;"></i>🚀 Start Setup Journey
                        </button>
                    </div>
                </div>

                <!-- Step 2: Database Creation -->
                <div class="deploy-step" id="database-step">
                    <div style="text-align: center; margin-bottom: 40px;">
                        <h3 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; font-size: 2rem; margin-bottom: 15px;">🗄️ Creating Database</h3>
                        <p style="color: #6c757d; font-size: 1.1rem;">Setting up your secure database structure and tables...</p>
                    </div>
                    <div class="progress-container">
                        <div class="progress">
                            <div class="progress-bar" id="db-progress" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="loading-spinner" id="db-spinner">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p style="margin-top: 20px; color: #667eea; font-weight: 600; font-size: 1.1rem;">⚡ Setting up database structure...</p>
                        <p style="color: #6c757d; font-size: 0.9rem; margin-top: 10px;">This may take a few moments. Please wait...</p>
                    </div>
                    <div class="status-message" id="db-status"></div>
                </div>

                <!-- Step 3: Admin Setup -->
                <div class="deploy-step" id="admin-step">
                    <div style="text-align: center; margin-bottom: 40px;">
                        <h3 style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; font-size: 2rem; margin-bottom: 15px;">👤 Create Admin Account</h3>
                        <p style="color: #6c757d; font-size: 1.1rem;">Set up your administrator credentials to manage the e-voting system securely.</p>
                    </div>

                    <div class="admin-form">
                        <form id="admin-form">
                            <div class="form-group">
                                <label for="admin-username"><i class="fas fa-user-tie"></i> 👤 Admin Username</label>
                                <input type="text" class="form-control" id="admin-username" name="username" required
                                       placeholder="Enter your admin username" value="admin">
                                <small class="form-text text-muted">💡 This will be used to log into the admin panel.</small>
                            </div>

                            <div class="form-group">
                                <label for="admin-password"><i class="fas fa-shield-alt"></i> 🔒 Admin Password</label>
                                <input type="password" class="form-control" id="admin-password" name="password" required
                                       placeholder="Enter a secure password">
                                <div class="password-strength">
                                    <small style="color: #6c757d;">🔐 Password strength: <span id="strength-text" style="font-weight: 600;">Not entered</span></small>
                                    <div class="strength-bar">
                                        <div class="strength-fill" id="strength-fill"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="admin-confirm"><i class="fas fa-check-double"></i> ✅ Confirm Password</label>
                                <input type="password" class="form-control" id="admin-confirm" name="confirm" required
                                       placeholder="Confirm your password">
                                <small class="form-text" id="password-match"></small>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn-deploy">
                                    <i class="fas fa-user-shield" style="margin-right: 10px;"></i>🛡️ Create Admin Account
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Step 4: Completion -->
                <div class="deploy-step" id="complete-step">
                    <div class="text-center">
                        <div style="margin-bottom: 30px;">
                            <div style="font-size: 5rem; margin-bottom: 20px;">🎉</div>
                            <h3 style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; font-size: 2.5rem; margin-bottom: 15px;">✅ Setup Successful!</h3>
                            <p style="color: #6c757d; font-size: 1.2rem; max-width: 600px; margin: 0 auto;">🎊 Congratulations! Your e-voting system has been successfully set up and is ready to use.</p>
                        </div>

                        <div class="row mt-5">
                            <div class="col-md-6">
                                <div style="background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(74, 144, 226, 0.05) 100%); border: 2px solid rgba(74, 144, 226, 0.2); border-radius: 20px; padding: 30px; margin-bottom: 20px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 35px rgba(74, 144, 226, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                    <div style="font-size: 3rem; margin-bottom: 15px;">🎓</div>
                                    <h5 style="color: #4a90e2; font-weight: 700; margin-bottom: 15px;">Student Portal</h5>
                                    <p style="color: #6c757d; margin-bottom: 20px;">Students can register and vote here</p>
                                    <a href="index.php" style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                        <i class="fas fa-external-link-alt" style="margin-right: 8px;"></i>🚀 Open Portal
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div style="background: linear-gradient(135deg, rgba(17, 153, 142, 0.1) 0%, rgba(17, 153, 142, 0.05) 100%); border: 2px solid rgba(17, 153, 142, 0.2); border-radius: 20px; padding: 30px; margin-bottom: 20px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 35px rgba(17, 153, 142, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                    <div style="font-size: 3rem; margin-bottom: 15px;">⚙️</div>
                                    <h5 style="color: #11998e; font-weight: 700; margin-bottom: 15px;">Admin Panel</h5>
                                    <p style="color: #6c757d; margin-bottom: 20px;">Manage elections and view results</p>
                                    <a href="admin/index.php" style="background: linear-gradient(135deg, #11998e 0%, #0d7377 100%); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                        <i class="fas fa-external-link-alt" style="margin-right: 8px;"></i>🛡️ Open Admin
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div style="background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%); border: 2px solid rgba(255, 193, 7, 0.3); border-radius: 15px; padding: 20px; margin-top: 30px; border-left: 5px solid #ffc107;">
                            <div style="display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-shield-alt" style="color: #ffc107; font-size: 1.5rem; margin-right: 15px;"></i>
                                <div>
                                    <strong style="color: #856404;">🔒 Security Reminder:</strong>
                                    <span style="color: #6c757d;"> Please delete or secure the setup.php file after successful deployment for security reasons.</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Already Deployed Section -->
                <?php if ($isDeployed): ?>
                <div class="deploy-step active" id="already-deployed">
                    <div class="text-center">
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        </div>
                        <h3 class="text-success mb-4">System Already Set Up!</h3>
                        <p class="text-muted mb-4">Your e-voting system was successfully set up on <?php echo date('F j, Y \a\t g:i A', strtotime($deploymentData['completed_at'] ?? '')); ?>.</p>

                        <?php if (isset($deploymentData['admin_username'])): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-user-shield mr-2"></i>
                            <strong>Admin Username:</strong> <?php echo htmlspecialchars($deploymentData['admin_username']); ?>
                        </div>
                        <?php endif; ?>

                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-home fa-2x text-primary mb-3"></i>
                                        <h5>Student Portal</h5>
                                        <p class="small text-muted">Students can register and vote here</p>
                                        <a href="index.php" class="btn btn-primary">
                                            <i class="fas fa-external-link-alt mr-1"></i>Open Portal
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-cogs fa-2x text-success mb-3"></i>
                                        <h5>Admin Panel</h5>
                                        <p class="small text-muted">Manage elections and view results</p>
                                        <a href="admin/index.php" class="btn btn-success">
                                            <i class="fas fa-external-link-alt mr-1"></i>Open Admin
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning mt-4">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <strong>Security Note:</strong> Consider deleting this setup file (setup.php) for security reasons.
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        let currentStep = 1;
        let deploymentData = {};

        // Show specific step
        function showStep(stepNumber) {
            // Hide all steps
            document.querySelectorAll('.deploy-step').forEach(step => {
                step.classList.remove('active');
            });

            // Update step indicators
            for (let i = 1; i <= 4; i++) {
                const stepElement = document.getElementById(`step${i}`);
                const labelElement = stepElement.parentElement.querySelector('.step-label');

                stepElement.classList.remove('active', 'completed');
                labelElement.classList.remove('active', 'completed');

                if (i < stepNumber) {
                    stepElement.classList.add('completed');
                    labelElement.classList.add('completed');
                } else if (i === stepNumber) {
                    stepElement.classList.add('active');
                    labelElement.classList.add('active');
                }
            }

            // Show current step
            const steps = {
                1: 'welcome-step',
                2: 'database-step',
                3: 'admin-step',
                4: 'complete-step'
            };

            document.getElementById(steps[stepNumber]).classList.add('active');
            currentStep = stepNumber;
        }

        // Start deployment process
        function startDeployment() {
            showStep(2);
            createDatabase();
        }

        // Create database
        function createDatabase() {
            const spinner = document.getElementById('db-spinner');
            const progress = document.getElementById('db-progress');
            const status = document.getElementById('db-status');

            spinner.style.display = 'block';

            // Simulate progress
            let progressValue = 0;
            const progressInterval = setInterval(() => {
                progressValue += Math.random() * 15;
                if (progressValue > 90) progressValue = 90;
                progress.style.width = progressValue + '%';
            }, 200);

            // Make AJAX request to create database
            $.ajax({
                url: 'setup.php',
                method: 'POST',
                data: { action: 'create_database' },
                dataType: 'json',
                success: function(data) {
                    clearInterval(progressInterval);
                    progress.style.width = '100%';
                    spinner.style.display = 'none';

                    if (data.success) {
                        status.className = 'status-message alert alert-success show';
                        status.innerHTML = '<i class="fas fa-check-circle mr-2"></i>' + data.message;

                        setTimeout(() => {
                            showStep(3);
                        }, 2000);
                    } else {
                        status.className = 'status-message alert alert-danger show';
                        status.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i>' + data.message;
                    }
                },
                error: function(xhr, status, error) {
                    clearInterval(progressInterval);
                    spinner.style.display = 'none';
                    const statusDiv = document.getElementById('db-status');
                    statusDiv.className = 'status-message alert alert-danger show';

                    let errorMessage = 'Connection failed. Please ensure:';
                    errorMessage += '<ul class="mt-2 mb-0">';
                    errorMessage += '<li>XAMPP/WAMP is running</li>';
                    errorMessage += '<li>Apache and MySQL services are started</li>';
                    errorMessage += '<li>You are accessing via localhost (not file://)</li>';
                    errorMessage += '<li>PHP is working correctly</li>';
                    errorMessage += '</ul>';
                    errorMessage += '<p class="mt-2 mb-0"><strong>Error details:</strong> ' + error + '</p>';

                    statusDiv.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i>' + errorMessage;
                }
            });
        }

        // Password strength checker
        function checkPasswordStrength(password) {
            let strength = 0;

            if (password.length >= 8) strength += 25;
            if (password.match(/[A-Z]/)) strength += 25;
            if (password.match(/[a-z]/)) strength += 25;
            if (password.match(/[\d\W]/)) strength += 25;

            return strength;
        }

        // Update password strength indicator
        function updatePasswordStrength() {
            const password = document.getElementById('admin-password').value;
            const strengthText = document.getElementById('strength-text');
            const strengthFill = document.getElementById('strength-fill');

            if (!password) {
                strengthText.textContent = 'Not entered';
                strengthFill.style.width = '0%';
                strengthFill.style.backgroundColor = '#e9ecef';
                return;
            }

            const strength = checkPasswordStrength(password);

            let text, color;
            if (strength < 25) {
                text = 'Very Weak';
                color = '#dc3545';
            } else if (strength < 50) {
                text = 'Weak';
                color = '#fd7e14';
            } else if (strength < 75) {
                text = 'Medium';
                color = '#ffc107';
            } else if (strength < 100) {
                text = 'Strong';
                color = '#20c997';
            } else {
                text = 'Very Strong';
                color = '#28a745';
            }

            strengthText.textContent = text;
            strengthFill.style.width = strength + '%';
            strengthFill.style.backgroundColor = color;
        }

        // Check password match
        function checkPasswordMatch() {
            const password = document.getElementById('admin-password').value;
            const confirm = document.getElementById('admin-confirm').value;
            const matchElement = document.getElementById('password-match');

            if (!confirm) {
                matchElement.textContent = '';
                return false;
            }

            if (password === confirm) {
                matchElement.textContent = '✓ Passwords match';
                matchElement.className = 'form-text text-success';
                return true;
            } else {
                matchElement.textContent = '✗ Passwords do not match';
                matchElement.className = 'form-text text-danger';
                return false;
            }
        }

        // Create admin account
        function createAdminAccount(formData) {
            const submitBtn = document.querySelector('#admin-form button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Account...';

            $.ajax({
                url: 'setup.php',
                method: 'POST',
                data: {
                    action: 'create_admin',
                    username: formData.get('username'),
                    password: formData.get('password')
                },
                dataType: 'json',
                success: function(data) {
                    if (data.success) {
                        deploymentData.adminCreated = true;
                        deploymentData.adminUsername = formData.get('username');
                        showStep(4);
                    } else {
                        alert('Error creating admin account: ' + data.message);
                    }
                },
                error: function(xhr, status, error) {
                    alert('Error: ' + error);
                },
                complete: function() {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            });
        }

        // Initialize event listeners
        $(document).ready(function() {
            // Password strength monitoring
            $('#admin-password').on('input', updatePasswordStrength);

            // Password match monitoring
            $('#admin-confirm').on('input', checkPasswordMatch);

            // Admin form submission
            $('#admin-form').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const password = formData.get('password');
                const confirm = formData.get('confirm');
                const username = formData.get('username');

                // Validation
                if (!username || username.length < 3) {
                    alert('Username must be at least 3 characters long');
                    return;
                }

                if (!password || password.length < 6) {
                    alert('Password must be at least 6 characters long');
                    return;
                }

                if (password !== confirm) {
                    alert('Passwords do not match');
                    return;
                }

                const strength = checkPasswordStrength(password);
                if (strength < 50) {
                    if (!confirm('Your password is weak. Continue anyway?')) {
                        return;
                    }
                }

                createAdminAccount(formData);
            });
        });
    </script>
</body>
</html>
