<?php
// Start session
session_start();

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['student_id'])) {
    echo json_encode(['success' => false, 'message' => 'You must be logged in to vote']);
    exit;
}

// Include database connection and functions
$conn = require_once '../database/db_config.php';
require_once '../includes/functions.php';

// Check if election is active
if (!isElectionActive()) {
    echo json_encode(['success' => false, 'message' => 'The election is not currently active']);
    exit;
}

// Get student ID
$student_id = $_SESSION['student_id'];

// Check if student has already voted
$stmt = $conn->prepare("SELECT has_voted FROM students WHERE id = ?");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

if ($student['has_voted']) {
    echo json_encode(['success' => false, 'message' => 'You have already voted']);
    exit;
}

// Get all positions
$positions = [];
$sql = "SELECT id FROM positions";
$positions_result = $conn->query($sql);

while ($position = $positions_result->fetch_assoc()) {
    $positions[] = $position['id'];
}

// Start transaction
$conn->begin_transaction();

try {
    // Process votes for each position
    foreach ($positions as $position_id) {
        $candidate_key = "candidate_" . $position_id;

        if (!isset($_POST[$candidate_key])) {
            throw new Exception("Please select a candidate for all positions");
        }

        $candidate_id = $_POST[$candidate_key];

        // Verify candidate exists and belongs to the position
        $stmt = $conn->prepare("SELECT id FROM candidates WHERE id = ? AND position_id = ?");
        $stmt->bind_param("ii", $candidate_id, $position_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            throw new Exception("Invalid candidate selection");
        }

        // Insert vote
        $stmt = $conn->prepare("INSERT INTO votes (student_id, candidate_id, position_id) VALUES (?, ?, ?)");
        $stmt->bind_param("iii", $student_id, $candidate_id, $position_id);
        $stmt->execute();
    }

    // Update student's voting status
    $stmt = $conn->prepare("UPDATE students SET has_voted = 1 WHERE id = ?");
    $stmt->bind_param("i", $student_id);
    $stmt->execute();

    // Commit transaction
    $conn->commit();

    echo json_encode(['success' => true, 'message' => 'Your vote has been recorded successfully']);
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

// Close connection
$conn->close();
?>
