# Ogbonnaya Onu E-Voting System

A real-time online voting platform for student elections at Ogbonnaya Onu University. This system allows students to cast votes and see live vote updates as they are cast.

## Features

- Real-time vote counting and result visualization
- Secure student authentication using matriculation numbers
- Admin dashboard for managing candidates and viewing results
- Mobile-responsive design for voting on any device
- Prevention of duplicate voting
- Detailed analytics and reporting

## Requirements

- PHP 7.2 or higher
- MySQL 5.7 or higher
- Web server (Apache, Nginx, etc.)
- Modern web browser

## Installation

For detailed installation instructions, please refer to the [INSTALLATION_GUIDE.md](INSTALLATION_GUIDE.md) file.

Quick overview:

1. Install XAMPP (or similar web server package)
2. Extract the files to your web server's document root
3. Navigate to `http://localhost/VOTE/install.php` in your web browser
4. Follow the on-screen instructions to set up the database

**Default admin credentials**
- Username: `admin`
- Password: `admin@2023`
- **Important:** Change the default password after first login

## Directory Structure

```
├── admin/                  # Admin panel files
├── api/                    # API endpoints
├── css/                    # CSS stylesheets
├── database/               # Database configuration and scripts
├── images/                 # System images
├── includes/               # Common include files
├── js/                     # JavaScript files
├── uploads/                # Uploaded files (candidate photos, etc.)
├── websocket/              # WebSocket server for real-time updates
├── index.php               # Homepage
├── login.php               # Student login page
├── register.php            # Student registration page
├── vote.php                # Voting interface
├── results.php             # Results page
└── README.md               # This file
```

## Usage

### For Students

1. **Register an account**
   - Navigate to the registration page
   - Enter your full name, matriculation number, department, and level
   - Create a password for your account

2. **Login to your account**
   - Use your matriculation number and password to login

3. **Cast your vote**
   - Select one candidate for each position
   - Submit your vote
   - Once submitted, you cannot change your vote

4. **View results**
   - After voting, you can view the real-time results

### For Administrators

1. **Login to the admin panel**
   - Navigate to `http://your-domain.com/admin/`
   - Use the admin credentials to login

2. **Manage candidates**
   - Add, edit, or remove candidates
   - Upload candidate photos
   - Assign candidates to positions

3. **View registered students**
   - See all registered students
   - Check their voting status
   - View individual student votes

4. **Monitor election results**
   - View real-time vote counts and percentages
   - See voting statistics by level
   - Export results to CSV

5. **System settings**
   - Change admin password
   - Reset votes (if needed)

## Real-time Updates

The system uses WebSockets for real-time updates. If WebSockets are not supported by the server, it falls back to AJAX polling.

To start the WebSocket server:

```
php websocket/server.php
```

For production environments, consider using a more robust WebSocket solution like Ratchet or Swoole.

## Security Considerations

- Change the default admin password immediately after installation
- Use HTTPS in production to encrypt data transmission
- Regularly backup the database
- Keep the server and PHP updated with security patches

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Credits

- Bootstrap 4.5.2 - https://getbootstrap.com/
- Chart.js - https://www.chartjs.org/
- Font Awesome 5.15.1 - https://fontawesome.com/
- jQuery 3.5.1 - https://jquery.com/
