<?php
// Start session
session_start();

// Include header
include 'includes/header.php';
?>

<div class="home-banner">
    <div class="home-banner-content">
        <?php if (isset($_SESSION['student_id'])): ?>
            <h1>Welcome, <?php echo isset($_SESSION['student_first_name']) ? htmlspecialchars($_SESSION['student_first_name']) : htmlspecialchars($_SESSION['student_name']); ?>!</h1>
            <p>Thank you for logging in to the Ogbonnaya Onu E-Voting System</p>
            <a href="vote.php" class="btn btn-primary btn-lg">Cast Your Vote</a>
        <?php else: ?>
            <h1>Welcome to Ogbonnaya Onu E-Voting System</h1>
            <p>A secure and transparent platform for student elections with real-time vote counting</p>
            <div>
                <a href="login.php" class="btn btn-primary btn-lg mr-3">Login to Vote</a>
                <a href="register.php" class="btn btn-outline-light btn-lg">Register</a>
            </div>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Secure Voting</h5>
            </div>
            <div class="card-body text-center">
                <i class="fas fa-lock fa-4x mb-3 text-primary"></i>
                <p>Our system ensures that each student can only vote once, maintaining the integrity of the election process.</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Real-Time Results</h5>
            </div>
            <div class="card-body text-center">
                <i class="fas fa-chart-bar fa-4x mb-3 text-primary"></i>
                <p>Watch the election unfold in real-time with live vote counting and dynamic result visualization.</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Easy to Use</h5>
            </div>
            <div class="card-body text-center">
                <i class="fas fa-user-check fa-4x mb-3 text-primary"></i>
                <p>Simple and intuitive interface makes voting quick and easy for all students.</p>
            </div>
        </div>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <h4 class="mb-0">How to Vote</h4>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 text-center">
                <div class="mb-3">
                    <i class="fas fa-user-plus fa-3x text-primary"></i>
                </div>
                <h5>1. Register</h5>
                <p>Create an account using your matriculation number and personal details.</p>
            </div>
            <div class="col-md-3 text-center">
                <div class="mb-3">
                    <i class="fas fa-sign-in-alt fa-3x text-primary"></i>
                </div>
                <h5>2. Login</h5>
                <p>Sign in with your matriculation number and password.</p>
            </div>
            <div class="col-md-3 text-center">
                <div class="mb-3">
                    <i class="fas fa-vote-yea fa-3x text-primary"></i>
                </div>
                <h5>3. Cast Your Vote</h5>
                <p>Select your preferred candidates for each position.</p>
            </div>
            <div class="col-md-3 text-center">
                <div class="mb-3">
                    <i class="fas fa-chart-line fa-3x text-primary"></i>
                </div>
                <h5>4. View Results</h5>
                <p>Watch the real-time results as votes are counted.</p>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
