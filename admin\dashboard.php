<?php
// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Direct database connection
$host = "localhost";
$username = "root";
$password = "";
$database = "voting_system";

// Connect to database
try {
    $conn = new mysqli($host, $username, $password, $database);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
} catch (Exception $e) {
    die("Database connection error: " . $e->getMessage());
}

// Check if election_settings table exists
$result = $conn->query("SHOW TABLES LIKE 'election_settings'");
if ($result->num_rows == 0) {
    die("Election settings table does not exist. Please run reset_database.php to set up the database.");
}

// Check if default settings exist
$result = $conn->query("SELECT COUNT(*) as count FROM election_settings");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    // Insert default settings
    $default_settings = [
        ['election_status', 'inactive'], // active, inactive
        ['election_title', 'Student Union Government Elections'],
        ['election_start_date', date('Y-m-d')],
        ['election_end_date', date('Y-m-d', strtotime('+1 week'))]
    ];

    foreach ($default_settings as $setting) {
        $key = $setting[0];
        $value = $setting[1];
        $sql = "INSERT INTO election_settings (setting_key, setting_value) VALUES ('$key', '$value')";
        $conn->query($sql);
    }
}

// Include functions
require_once '../includes/functions.php';

// Handle election control
$message = '';
$error = '';

if (isset($_POST['start_election'])) {
    if (updateSetting('election_status', 'active')) {
        $message = 'Election has been started successfully';
    } else {
        $error = 'Failed to start election';
    }
} elseif (isset($_POST['stop_election'])) {
    if (updateSetting('election_status', 'inactive')) {
        $message = 'Election has been stopped successfully';
    } else {
        $error = 'Failed to stop election';
    }
}

// Get current election status
$election_status = getSetting('election_status', 'inactive');

// Get statistics
// Total registered students
$sql = "SELECT COUNT(*) as total FROM students";
$result = $conn->query($sql);
$total_students = $result->fetch_assoc()['total'];

// Total votes cast
$sql = "SELECT COUNT(DISTINCT student_id) as total FROM votes";
$result = $conn->query($sql);
$total_votes = $result->fetch_assoc()['total'];

// Voter turnout
$voter_turnout = $total_students > 0 ? round(($total_votes / $total_students) * 100) : 0;

// Total candidates
$sql = "SELECT COUNT(*) as total FROM candidates";
$result = $conn->query($sql);
$total_candidates = $result->fetch_assoc()['total'];

// Recent votes
$recent_votes = [];
$sql = "SELECT v.id, s.full_name as student_name, s.matric_no, c.full_name as candidate_name,
        p.position_name, v.created_at
        FROM votes v
        JOIN students s ON v.student_id = s.id
        JOIN candidates c ON v.candidate_id = c.id
        JOIN positions p ON v.position_id = p.id
        ORDER BY v.created_at DESC
        LIMIT 5";
$result = $conn->query($sql);

while ($vote = $result->fetch_assoc()) {
    $recent_votes[] = $vote;
}

// Get pending password reset requests
$pending_reset_requests = getPendingPasswordResetRequests();

// Include admin header
include 'includes/header.php';
?>

<div class="container-fluid p-0">
    <div class="row no-gutters">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main content -->
        <main class="col-md-9 ml-sm-auto col-lg-10 p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h1 class="h2 mb-0">Dashboard</h1>
                <div class="text-muted small">
                    <i class="fas fa-clock"></i> <?php echo date('M d, Y H:i'); ?>
                </div>
            </div>

            <?php if (isset($_SESSION['message'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['message']; ?></div>
                <?php unset($_SESSION['message']); ?>
            <?php elseif ($message): ?>
                <div class="alert alert-success"><?php echo $message; ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; ?></div>
                <?php unset($_SESSION['error']); ?>
            <?php elseif ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <!-- Election Control -->
            <div class="card mb-2">
                <div class="card-header bg-primary text-white py-2">
                    <h5 class="mb-0"><i class="fas fa-power-off mr-2"></i>Election Control</h5>
                </div>
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-1">Current Status:
                                <?php if ($election_status === 'active'): ?>
                                    <span class="badge badge-success px-2 py-1">
                                        <i class="fas fa-play mr-1"></i>Active
                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-danger px-2 py-1">
                                        <i class="fas fa-stop mr-1"></i>Inactive
                                    </span>
                                <?php endif; ?>
                            </h5>
                            <p class="mb-0 text-muted small">
                                <?php if ($election_status === 'active'): ?>
                                    Students can currently vote in the election.
                                <?php else: ?>
                                    Students cannot vote at this time.
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="col-md-6 text-right">
                            <form method="post" action="dashboard.php" class="d-inline">
                                <?php if ($election_status === 'active'): ?>
                                    <button type="submit" name="stop_election" class="btn btn-danger" onclick="return confirm('Are you sure you want to stop the election? Students will no longer be able to vote.')">
                                        <i class="fas fa-stop-circle mr-2"></i>Stop Election
                                    </button>
                                <?php else: ?>
                                    <button type="submit" name="start_election" class="btn btn-success" onclick="return confirm('Are you sure you want to start the election? Students will be able to vote.')">
                                        <i class="fas fa-play-circle mr-2"></i>Start Election
                                    </button>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Password Reset Requests -->
            <?php if (!empty($pending_reset_requests)): ?>
            <div class="card mb-2">
                <div class="card-header bg-warning text-dark py-2">
                    <h5 class="mb-0">
                        <i class="fas fa-key mr-2"></i>Pending Password Reset Requests
                        <span class="badge badge-dark ml-2"><?php echo count($pending_reset_requests); ?></span>
                    </h5>
                </div>
                <div class="card-body py-1">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Student</th>
                                    <th>Matric No</th>
                                    <th>Requested</th>
                                    <th width="200">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pending_reset_requests as $request): ?>
                                    <tr>
                                        <td><?php echo $request['full_name']; ?></td>
                                        <td><?php echo $request['matric_no']; ?></td>
                                        <td><?php echo date('M d, Y H:i', strtotime($request['created_at'])); ?></td>
                                        <td>
                                            <a href="approve_reset.php?token=<?php echo $request['token']; ?>&action=approve" class="btn btn-sm btn-success mr-1" onclick="return confirm('Are you sure you want to approve this password reset request?')">
                                                <i class="fas fa-check"></i> Approve
                                            </a>
                                            <a href="approve_reset.php?token=<?php echo $request['token']; ?>&action=reject" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to reject this password reset request?')">
                                                <i class="fas fa-times"></i> Reject
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Statistics cards -->
            <div class="row mb-2">
                <div class="col-lg-3 col-md-6 mb-2">
                    <div class="card dashboard-card h-100">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-users fa-3x text-primary mb-2"></i>
                            <h5 class="mb-2">Registered Students</h5>
                            <h3 class="text-primary font-weight-bold mb-0"><?php echo number_format($total_students); ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-2">
                    <div class="card dashboard-card h-100">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-vote-yea fa-3x text-primary mb-2"></i>
                            <h5 class="mb-2">Votes Cast</h5>
                            <h3 class="text-primary font-weight-bold mb-0"><?php echo number_format($total_votes); ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-2">
                    <div class="card dashboard-card h-100">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-chart-pie fa-3x text-primary mb-2"></i>
                            <h5 class="mb-2">Voter Turnout</h5>
                            <h3 class="text-primary font-weight-bold mb-0"><?php echo $voter_turnout; ?>%</h3>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-2">
                    <div class="card dashboard-card h-100">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-user-tie fa-3x text-primary mb-2"></i>
                            <h5 class="mb-2">Candidates</h5>
                            <h3 class="text-primary font-weight-bold mb-0"><?php echo number_format($total_candidates); ?></h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent votes -->
            <div class="card mb-2">
                <div class="card-header bg-primary text-white py-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-history mr-2"></i>Recent Votes</h5>
                        <a href="votes.php" class="btn btn-sm btn-outline-light">View All</a>
                    </div>
                </div>
                <div class="card-body py-1">
                    <?php if (count($recent_votes) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm mb-0">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Student</th>
                                        <th>Matric No</th>
                                        <th>Position</th>
                                        <th>Candidate</th>
                                        <th>Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_votes as $vote): ?>
                                        <tr>
                                            <td><?php echo $vote['student_name']; ?></td>
                                            <td><span class="badge badge-secondary"><?php echo $vote['matric_no']; ?></span></td>
                                            <td><span class="badge badge-primary"><?php echo $vote['position_name']; ?></span></td>
                                            <td><?php echo $vote['candidate_name']; ?></td>
                                            <td class="text-muted"><?php echo date('M d, H:i', strtotime($vote['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-2">
                            <i class="fas fa-vote-yea fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No votes have been cast yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick links -->
            <div class="row mb-2">
                <div class="col-lg-4 col-md-6 mb-2">
                    <div class="card h-100">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-user-tie fa-3x text-primary mb-2"></i>
                            <h5 class="mb-2">Manage Candidates</h5>
                            <p class="text-muted mb-3">Add, edit, or remove candidates</p>
                            <a href="candidates.php" class="btn btn-primary">
                                <i class="fas fa-arrow-right mr-1"></i>Manage
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-2">
                    <div class="card h-100">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-users fa-3x text-primary mb-2"></i>
                            <h5 class="mb-2">View Students</h5>
                            <p class="text-muted mb-3">View registered students & status</p>
                            <a href="students.php" class="btn btn-primary">
                                <i class="fas fa-arrow-right mr-1"></i>View
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-2">
                    <div class="card h-100">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-chart-bar fa-3x text-primary mb-2"></i>
                            <h5 class="mb-2">Election Results</h5>
                            <p class="text-muted mb-3">View detailed results & statistics</p>
                            <a href="results.php" class="btn btn-primary">
                                <i class="fas fa-arrow-right mr-1"></i>Results
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
// Include admin footer
include 'includes/footer.php';
?>
