<?php
// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Direct database connection
$host = "localhost";
$username = "root";
$password = "";
$database = "voting_system";

// Connect to database
try {
    $conn = new mysqli($host, $username, $password, $database);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
} catch (Exception $e) {
    die("Database connection error: " . $e->getMessage());
}

// Check if election_settings table exists
$result = $conn->query("SHOW TABLES LIKE 'election_settings'");
if ($result->num_rows == 0) {
    die("Election settings table does not exist. Please run reset_database.php to set up the database.");
}

// Check if default settings exist
$result = $conn->query("SELECT COUNT(*) as count FROM election_settings");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    // Insert default settings
    $default_settings = [
        ['election_status', 'inactive'], // active, inactive
        ['election_title', 'Student Union Government Elections'],
        ['election_start_date', date('Y-m-d')],
        ['election_end_date', date('Y-m-d', strtotime('+1 week'))]
    ];

    foreach ($default_settings as $setting) {
        $key = $setting[0];
        $value = $setting[1];
        $sql = "INSERT INTO election_settings (setting_key, setting_value) VALUES ('$key', '$value')";
        $conn->query($sql);
    }
}

// Include functions
require_once '../includes/functions.php';

// Handle election control
$message = '';
$error = '';

if (isset($_POST['start_election'])) {
    if (updateSetting('election_status', 'active')) {
        $message = 'Election has been started successfully';
    } else {
        $error = 'Failed to start election';
    }
} elseif (isset($_POST['stop_election'])) {
    if (updateSetting('election_status', 'inactive')) {
        $message = 'Election has been stopped successfully';
    } else {
        $error = 'Failed to stop election';
    }
}

// Get current election status
$election_status = getSetting('election_status', 'inactive');

// Get statistics
// Total registered students
$sql = "SELECT COUNT(*) as total FROM students";
$result = $conn->query($sql);
$total_students = $result->fetch_assoc()['total'];

// Total votes cast
$sql = "SELECT COUNT(DISTINCT student_id) as total FROM votes";
$result = $conn->query($sql);
$total_votes = $result->fetch_assoc()['total'];

// Voter turnout
$voter_turnout = $total_students > 0 ? round(($total_votes / $total_students) * 100) : 0;

// Total candidates
$sql = "SELECT COUNT(*) as total FROM candidates";
$result = $conn->query($sql);
$total_candidates = $result->fetch_assoc()['total'];

// Recent votes
$recent_votes = [];
$sql = "SELECT v.id, s.full_name as student_name, s.matric_no, c.full_name as candidate_name,
        p.position_name, v.created_at
        FROM votes v
        JOIN students s ON v.student_id = s.id
        JOIN candidates c ON v.candidate_id = c.id
        JOIN positions p ON v.position_id = p.id
        ORDER BY v.created_at DESC
        LIMIT 5";
$result = $conn->query($sql);

while ($vote = $result->fetch_assoc()) {
    $recent_votes[] = $vote;
}

// Get pending password reset requests
$pending_reset_requests = getPendingPasswordResetRequests();

// Include admin header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main content -->
        <main class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
            <h1 class="h2 mb-4">Dashboard</h1>

            <?php if (isset($_SESSION['message'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['message']; ?></div>
                <?php unset($_SESSION['message']); ?>
            <?php elseif ($message): ?>
                <div class="alert alert-success"><?php echo $message; ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; ?></div>
                <?php unset($_SESSION['error']); ?>
            <?php elseif ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <!-- Election Control -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Election Control</h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4>Current Status:
                                <?php if ($election_status === 'active'): ?>
                                    <span class="badge badge-success">Active</span>
                                <?php else: ?>
                                    <span class="badge badge-danger">Inactive</span>
                                <?php endif; ?>
                            </h4>
                            <p>
                                <?php if ($election_status === 'active'): ?>
                                    Students can currently vote in the election.
                                <?php else: ?>
                                    Students cannot vote at this time.
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="col-md-6 text-right">
                            <form method="post" action="dashboard.php" class="d-inline">
                                <?php if ($election_status === 'active'): ?>
                                    <button type="submit" name="stop_election" class="btn btn-danger" onclick="return confirm('Are you sure you want to stop the election? Students will no longer be able to vote.')">
                                        <i class="fas fa-stop-circle"></i> Stop Election
                                    </button>
                                <?php else: ?>
                                    <button type="submit" name="start_election" class="btn btn-success" onclick="return confirm('Are you sure you want to start the election? Students will be able to vote.')">
                                        <i class="fas fa-play-circle"></i> Start Election
                                    </button>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Password Reset Requests -->
            <?php if (!empty($pending_reset_requests)): ?>
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">Pending Password Reset Requests (<?php echo count($pending_reset_requests); ?>)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Student</th>
                                    <th>Matric No</th>
                                    <th>Requested</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pending_reset_requests as $request): ?>
                                    <tr>
                                        <td><?php echo $request['full_name']; ?></td>
                                        <td><?php echo $request['matric_no']; ?></td>
                                        <td><?php echo date('M d, Y H:i', strtotime($request['created_at'])); ?></td>
                                        <td>
                                            <a href="approve_reset.php?token=<?php echo $request['token']; ?>&action=approve" class="btn btn-sm btn-success" onclick="return confirm('Are you sure you want to approve this password reset request?')">
                                                <i class="fas fa-check"></i> Approve
                                            </a>
                                            <a href="approve_reset.php?token=<?php echo $request['token']; ?>&action=reject" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to reject this password reset request?')">
                                                <i class="fas fa-times"></i> Reject
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Statistics cards -->
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="card dashboard-card">
                        <i class="fas fa-users"></i>
                        <h3>Registered Students</h3>
                        <p><?php echo $total_students; ?></p>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card dashboard-card">
                        <i class="fas fa-vote-yea"></i>
                        <h3>Votes Cast</h3>
                        <p><?php echo $total_votes; ?></p>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card dashboard-card">
                        <i class="fas fa-chart-pie"></i>
                        <h3>Voter Turnout</h3>
                        <p><?php echo $voter_turnout; ?>%</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card dashboard-card">
                        <i class="fas fa-user-tie"></i>
                        <h3>Candidates</h3>
                        <p><?php echo $total_candidates; ?></p>
                    </div>
                </div>
            </div>

            <!-- Recent votes -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Recent Votes</h5>
                </div>
                <div class="card-body">
                    <?php if (count($recent_votes) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Matric No</th>
                                        <th>Position</th>
                                        <th>Candidate</th>
                                        <th>Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_votes as $vote): ?>
                                        <tr>
                                            <td><?php echo $vote['student_name']; ?></td>
                                            <td><?php echo $vote['matric_no']; ?></td>
                                            <td><?php echo $vote['position_name']; ?></td>
                                            <td><?php echo $vote['candidate_name']; ?></td>
                                            <td><?php echo date('M d, Y H:i', strtotime($vote['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="votes.php" class="btn btn-primary">View All Votes</a>
                        </div>
                    <?php else: ?>
                        <p class="text-center">No votes have been cast yet.</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick links -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">Manage Candidates</h5>
                        </div>
                        <div class="card-body">
                            <p>Add, edit, or remove candidates for the election.</p>
                            <a href="candidates.php" class="btn btn-primary">Manage Candidates</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">View Students</h5>
                        </div>
                        <div class="card-body">
                            <p>View all registered students and their voting status.</p>
                            <a href="students.php" class="btn btn-primary">View Students</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">Election Results</h5>
                        </div>
                        <div class="card-body">
                            <p>View detailed election results and statistics.</p>
                            <a href="results.php" class="btn btn-primary">View Results</a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
// Include admin footer
include 'includes/footer.php';
?>
