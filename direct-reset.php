<?php
// Set page title
$pageTitle = "Admin Password Reset";

// Direct database connection
$host = "localhost";
$username = "root";
$password = "";
$database = "voting_system";

// Initialize variables
$error = '';
$success = '';
$resetComplete = false;
$adminExists = false;
$adminUsername = '';

// Connect to database
try {
    $conn = new mysqli($host, $username, $password, $database);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
} catch (Exception $e) {
    die("Database connection error: " . $e->getMessage());
}

// Check if admins table exists
$result = $conn->query("SHOW TABLES LIKE 'admins'");
if ($result->num_rows == 0) {
    die("Admins table does not exist. Please run importdb.php to set up the database.");
}

// Check if admin user exists and get username
$result = $conn->query("SELECT id, username FROM admins LIMIT 1");
if ($result->num_rows > 0) {
    $adminExists = true;
    $admin = $result->fetch_assoc();
    $adminUsername = $admin['username'];
} else {
    $error = 'No admin account found in the database. Please run importdb.php to set up the database.';
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $adminExists) {
    // Validate input
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $security_code = $_POST['security_code'] ?? '';

    if (empty($new_password) || empty($confirm_password)) {
        $error = 'Please enter both password fields';
    } elseif ($new_password !== $confirm_password) {
        $error = 'Passwords do not match';
    } elseif (strlen($new_password) < 6) {
        $error = 'Password must be at least 6 characters long';
    } elseif ($security_code !== 'RESET2023') {
        $error = 'Invalid security code. Please enter the correct code to proceed.';
    } else {
        // Hash new password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

        // Update admin password
        $stmt = $conn->prepare("UPDATE admins SET password = ? WHERE username = ?");
        $stmt->bind_param("ss", $hashed_password, $adminUsername);

        if ($stmt->execute() && $stmt->affected_rows > 0) {
            $success = 'Admin password has been reset successfully! You can now log in with the new password.';
            $resetComplete = true;

            // Log the password reset
            $ip = $_SERVER['REMOTE_ADDR'];
            $date = date('Y-m-d H:i:s');
            $logMessage = "Admin password reset performed from IP: $ip on $date\n";
            @file_put_contents('admin_reset_log.txt', $logMessage, FILE_APPEND);
        } else {
            $error = 'Failed to reset admin password. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Ogbonnaya Onu E-Voting System</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .reset-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            background-color: #fff;
        }
        .btn-reset {
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
        }
        .alert {
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        .security-notice {
            font-size: 14px;
            color: #6c757d;
            margin-top: 20px;
            padding: 10px;
            border-left: 3px solid #ffc107;
            background-color: #fff8e1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="reset-container">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Admin Password Reset</h4>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                        <div class="text-center mt-3">
                            <a href="admin/index.php" class="btn btn-primary">Go to Admin Login</a>
                        </div>
                    <?php elseif (!$resetComplete): ?>
                        <p>This page allows you to reset the admin password for the account <strong><?php echo htmlspecialchars($adminUsername); ?></strong>. Enter a new password below.</p>

                        <form method="post" action="direct-reset.php">
                            <div class="form-group">
                                <label for="new_password">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <div class="mt-2">
                                    <span>Password Strength: </span>
                                    <span id="password-strength" class="badge badge-secondary">Not entered</span>
                                </div>
                                <small class="form-text text-muted">Password must be at least 6 characters long. For a strong password, use a mix of uppercase and lowercase letters, numbers, and special characters.</small>
                            </div>

                            <div class="form-group">
                                <label for="confirm_password">Confirm Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <div class="mt-2">
                                    <span id="password-match" class="small"></span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="security_code">Security Code</label>
                                <input type="text" class="form-control" id="security_code" name="security_code" required>
                                <small class="form-text text-muted">Enter the security code: RESET2023</small>
                            </div>

                            <button type="submit" class="btn btn-primary btn-block btn-reset">Reset Admin Password</button>
                        </form>

                        <div class="security-notice">
                            <strong>Security Notice:</strong> This page should be deleted after use to prevent unauthorized access. After resetting the password, please delete the direct-reset.php file from your server.
                        </div>

                        <div class="alert alert-warning mt-3">
                            <strong>Important:</strong> This will reset the password for the admin account. The current password will no longer work after this change.
                        </div>
                    <?php endif; ?>

                    <div class="text-center mt-3">
                        <a href="index.php">Back to Home</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        // Password strength meter
        $(document).ready(function() {
            $('#new_password').on('input', function() {
                var password = $(this).val();
                var strength = 0;

                // Check password length
                if (password.length >= 8) {
                    strength += 1;
                }

                // Check for mixed case
                if (password.match(/[a-z]/) && password.match(/[A-Z]/)) {
                    strength += 1;
                }

                // Check for numbers
                if (password.match(/\d/)) {
                    strength += 1;
                }

                // Check for special characters
                if (password.match(/[^a-zA-Z\d]/)) {
                    strength += 1;
                }

                // Update strength meter
                var strengthMeter = $('#password-strength');
                var strengthText = '';
                var strengthClass = '';

                switch (strength) {
                    case 0:
                        strengthText = 'Very Weak';
                        strengthClass = 'bg-danger';
                        break;
                    case 1:
                        strengthText = 'Weak';
                        strengthClass = 'bg-warning';
                        break;
                    case 2:
                        strengthText = 'Medium';
                        strengthClass = 'bg-info';
                        break;
                    case 3:
                        strengthText = 'Strong';
                        strengthClass = 'bg-primary';
                        break;
                    case 4:
                        strengthText = 'Very Strong';
                        strengthClass = 'bg-success';
                        break;
                }

                // Update the strength meter
                strengthMeter.html(strengthText);
                strengthMeter.removeClass('badge-danger badge-warning badge-info badge-primary badge-success badge-secondary');
                strengthMeter.addClass(strengthClass.replace('bg-', 'badge-'));
            });

            // Check if passwords match
            $('#confirm_password').on('input', function() {
                var password = $('#new_password').val();
                var confirmPassword = $(this).val();

                if (password === confirmPassword) {
                    $('#password-match').html('Passwords match').removeClass('text-danger').addClass('text-success');
                } else {
                    $('#password-match').html('Passwords do not match').removeClass('text-success').addClass('text-danger');
                }
            });
        });
    </script>
</body>
</html>
