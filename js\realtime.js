// Fallback AJAX-based real-time updates for browsers that don't support WebSockets

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the results page
    const resultsContainer = document.querySelector('.results-container');
    
    if (resultsContainer) {
        // Start polling for updates
        startPolling();
    }
});

// Function to start polling for updates
function startPolling() {
    // Initial fetch
    fetchResults();
    
    // Set up interval for polling (every 3 seconds)
    setInterval(fetchResults, 3000);
}

// Function to fetch results via AJAX
function fetchResults() {
    // Add a random parameter to prevent caching
    const timestamp = new Date().getTime();
    
    fetch(`api/results.php?t=${timestamp}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the UI with new results
                updateResults(data.results);
            }
        })
        .catch(error => console.error('Error fetching results:', error));
}

// Function to update the UI with new results
function updateResults(results) {
    results.forEach(position => {
        position.candidates.forEach(candidate => {
            // Update vote count
            const voteCountElement = document.getElementById(`vote-count-${candidate.id}`);
            if (voteCountElement) {
                const currentCount = parseInt(voteCountElement.textContent);
                
                // Add animation if the count has changed
                if (currentCount !== candidate.votes) {
                    voteCountElement.classList.add('real-time-update');
                    setTimeout(() => {
                        voteCountElement.classList.remove('real-time-update');
                    }, 2000);
                    
                    voteCountElement.textContent = candidate.votes;
                }
            }
            
            // Update progress bar
            const progressBar = document.getElementById(`progress-${candidate.id}`);
            if (progressBar) {
                const total = position.candidates.reduce((sum, c) => sum + c.votes, 0);
                const percentage = total > 0 ? Math.round((candidate.votes / total) * 100) : 0;
                
                progressBar.style.width = `${percentage}%`;
                progressBar.setAttribute('aria-valuenow', percentage);
                
                // Update percentage text
                const percentElement = document.getElementById(`vote-percent-${candidate.id}`);
                if (percentElement) {
                    percentElement.textContent = `${percentage}%`;
                }
            }
        });
        
        // Update chart if it exists
        updateChart(position);
    });
}

// Function to update a chart with new data
function updateChart(position) {
    const chartCanvas = document.getElementById(`chart-${position.position_id}`);
    if (chartCanvas) {
        const chart = Chart.getChart(chartCanvas);
        
        if (chart) {
            // Update chart data
            chart.data.datasets[0].data = position.candidates.map(candidate => candidate.votes);
            chart.update();
        }
    }
}
