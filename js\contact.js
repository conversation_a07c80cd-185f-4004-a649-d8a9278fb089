// Contact page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Add animation to contact info sections
    const contactSections = document.querySelectorAll('.contact-icon');

    contactSections.forEach((icon, index) => {
        // Add a slight delay to each item for a staggered animation
        setTimeout(() => {
            icon.style.opacity = '0';
            icon.style.transform = 'scale(0.8)';

            setTimeout(() => {
                icon.style.transition = 'all 0.5s ease';
                icon.style.opacity = '1';
                icon.style.transform = 'scale(1)';
            }, 50);
        }, index * 150);
    });

    // Add animation to social media icons
    const socialIcons = document.querySelectorAll('.social-icons-container .social-circle');

    socialIcons.forEach((icon, index) => {
        // Initial state
        icon.style.opacity = '0';
        icon.style.transform = 'translateY(20px)';

        // Animate in with delay
        setTimeout(() => {
            icon.style.transition = 'all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            icon.style.opacity = '1';
            icon.style.transform = 'translateY(0)';
        }, 300 + (index * 150));
    });

    // Add subtle continuous animation to social icons
    function startSocialIconsAnimation() {
        const icons = document.querySelectorAll('.social-icons-container .social-circle');
        icons.forEach((icon, index) => {
            // Set a different animation delay for each icon
            setTimeout(() => {
                icon.style.animation = `socialIconPulse 3s infinite ${index * 0.5}s`;
            }, index * 100);
        });
    }

    // Start the animation after initial entrance animation
    setTimeout(startSocialIconsAnimation, 1500);

    // Make map interactive on hover
    const mapContainer = document.querySelector('.embed-responsive');
    if (mapContainer) {
        mapContainer.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1)';
            this.style.transform = 'scale(1.02)';
            this.style.transition = 'all 0.3s ease';
        });

        mapContainer.addEventListener('mouseleave', function() {
            this.style.boxShadow = 'none';
            this.style.transform = 'scale(1)';
        });
    }
});
