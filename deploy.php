<?php
// Database configuration
$host = "localhost";
$username = "root";
$password = "";
$database = "voting_system";

// Check if already deployed
$deploymentStatusFile = 'deployment_status.json';
$isDeployed = false;
$deploymentData = null;

if (file_exists($deploymentStatusFile)) {
    $deploymentData = json_decode(file_get_contents($deploymentStatusFile), true);
    $isDeployed = isset($deploymentData['deployment_completed']) && $deploymentData['deployment_completed'];
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    if ($_POST['action'] === 'create_database') {
        try {
            // Connect to MySQL server without selecting a database
            $conn = new mysqli($host, $username, $password);

            if ($conn->connect_error) {
                throw new Exception("Connection failed: " . $conn->connect_error);
            }

            // Create database if it doesn't exist
            $sql = "CREATE DATABASE IF NOT EXISTS $database";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating database: " . $conn->error);
            }

            // Select the database
            $conn->select_db($database);

            // Create Students table
            $sql = "CREATE TABLE IF NOT EXISTS students (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(255) NOT NULL,
                matric_no VARCHAR(50) NOT NULL UNIQUE,
                department VARCHAR(255) NOT NULL,
                level ENUM('ND1', 'ND2', 'HND1', 'HND2') NOT NULL,
                password VARCHAR(255) NOT NULL,
                has_voted BOOLEAN DEFAULT FALSE,
                approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating students table: " . $conn->error);
            }

            // Create Positions table
            $sql = "CREATE TABLE IF NOT EXISTS positions (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                position_name VARCHAR(255) NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating positions table: " . $conn->error);
            }

            // Insert default positions
            $positions = ["President", "Director of Sports", "Director of Information"];
            foreach ($positions as $position) {
                $sql = "INSERT IGNORE INTO positions (position_name) VALUES ('$position')";
                $conn->query($sql);
            }

            // Create Candidates table
            $sql = "CREATE TABLE IF NOT EXISTS candidates (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(255) NOT NULL,
                position_id INT(11) NOT NULL,
                program VARCHAR(255) NOT NULL,
                photo VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (position_id) REFERENCES positions(id)
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating candidates table: " . $conn->error);
            }

            // Create Votes table
            $sql = "CREATE TABLE IF NOT EXISTS votes (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                student_id INT(11) NOT NULL,
                candidate_id INT(11) NOT NULL,
                position_id INT(11) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students(id),
                FOREIGN KEY (candidate_id) REFERENCES candidates(id),
                FOREIGN KEY (position_id) REFERENCES positions(id),
                UNIQUE KEY unique_vote (student_id, position_id)
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating votes table: " . $conn->error);
            }

            // Create Admin table
            $sql = "CREATE TABLE IF NOT EXISTS admins (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating admins table: " . $conn->error);
            }

            // Create Election Settings table
            $sql = "CREATE TABLE IF NOT EXISTS election_settings (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(50) NOT NULL UNIQUE,
                setting_value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating election_settings table: " . $conn->error);
            }

            // Insert default settings
            $default_settings = [
                ['election_status', 'inactive'],
                ['election_title', 'Student Union Government Elections'],
                ['election_start_date', date('Y-m-d')],
                ['election_end_date', date('Y-m-d', strtotime('+1 week'))]
            ];

            foreach ($default_settings as $setting) {
                $key = $setting[0];
                $value = $setting[1];
                $sql = "INSERT IGNORE INTO election_settings (setting_key, setting_value) VALUES ('$key', '$value')";
                $conn->query($sql);
            }

            // Create Password Reset Requests table
            $sql = "CREATE TABLE IF NOT EXISTS password_reset_requests (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                student_id INT(11) NOT NULL,
                token VARCHAR(255) NOT NULL,
                status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students(id),
                UNIQUE KEY (student_id, status)
            )";
            if (!$conn->query($sql)) {
                throw new Exception("Error creating password_reset_requests table: " . $conn->error);
            }

            $conn->close();

            // Update deployment status
            $deploymentStatus = [
                'database_created' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'version' => '1.0.0'
            ];
            file_put_contents($deploymentStatusFile, json_encode($deploymentStatus, JSON_PRETTY_PRINT));

            echo json_encode(['success' => true, 'message' => 'Database created successfully!']);

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }

    if ($_POST['action'] === 'create_admin') {
        try {
            $admin_username = trim($_POST['username']);
            $admin_password = $_POST['password'];

            if (empty($admin_username) || strlen($admin_username) < 3) {
                throw new Exception('Username must be at least 3 characters long');
            }

            if (empty($admin_password) || strlen($admin_password) < 6) {
                throw new Exception('Password must be at least 6 characters long');
            }

            // Connect to database
            $conn = new mysqli($host, $username, $password, $database);

            if ($conn->connect_error) {
                throw new Exception("Connection failed: " . $conn->connect_error);
            }

            // Hash password and create admin
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO admins (username, password) VALUES (?, ?) ON DUPLICATE KEY UPDATE password = ?");
            $stmt->bind_param("sss", $admin_username, $hashed_password, $hashed_password);

            if (!$stmt->execute()) {
                throw new Exception("Failed to create admin account");
            }

            $conn->close();

            // Update deployment status
            $deploymentStatus = json_decode(file_get_contents($deploymentStatusFile), true);
            $deploymentStatus['admin_created'] = true;
            $deploymentStatus['admin_username'] = $admin_username;
            $deploymentStatus['deployment_completed'] = true;
            $deploymentStatus['completed_at'] = date('Y-m-d H:i:s');
            file_put_contents($deploymentStatusFile, json_encode($deploymentStatus, JSON_PRETTY_PRINT));

            echo json_encode(['success' => true, 'message' => 'Admin account created successfully!']);

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deploy E-Voting System - Ogbonnaya Onu Polytechnic</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #2c3e50;
            line-height: 1.6;
        }

        .deploy-container {
            max-width: 900px;
            margin: 40px auto;
            padding: 20px;
        }

        .deployment-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .deploy-header {
            background: white;
            color: #2c3e50;
            padding: 40px 30px 30px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
        }

        .deploy-header h1 {
            font-size: 2.2rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .deploy-header .subtitle {
            font-size: 1rem;
            color: #6c757d;
            font-weight: 400;
        }

        .deploy-header .institution {
            font-size: 0.9rem;
            color: #007bff;
            font-weight: 500;
            margin-top: 5px;
        }

        .deploy-content {
            padding: 30px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 20px;
            text-align: center;
        }

        .step-number {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .step-number.active {
            background: #007bff;
            color: white;
            border-color: #0056b3;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .step-number.completed {
            background: #28a745;
            color: white;
            border-color: #1e7e34;
        }

        .step-label {
            font-size: 0.8rem;
            font-weight: 500;
            color: #6c757d;
            margin: 0;
        }

        .step-label.active {
            color: #007bff;
            font-weight: 600;
        }

        .step-label.completed {
            color: #28a745;
        }

        .step-arrow {
            color: #dee2e6;
            margin: 0 10px;
            font-size: 1rem;
            align-self: flex-start;
            margin-top: 18px;
        }

        .deploy-step {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .deploy-step.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }

        .feature-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .feature-card h4 {
            color: #2c3e50;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .feature-card h4 i {
            margin-right: 10px;
            color: #007bff;
            font-size: 1.2rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            padding: 8px 0;
            position: relative;
            padding-left: 25px;
            color: #495057;
            font-size: 0.9rem;
        }

        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
            font-size: 1rem;
        }

        .btn-deploy {
            background: #007bff;
            border: none;
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 6px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            border: 2px solid transparent;
        }

        .btn-deploy:hover {
            background: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
            color: white;
            text-decoration: none;
        }

        .btn-deploy:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-deploy.btn-success {
            background: #28a745;
            border-color: #28a745;
        }

        .btn-deploy.btn-success:hover {
            background: #218838;
            border-color: #1e7e34;
            box-shadow: 0 4px 12px rgba(40,167,69,0.3);
        }

        .progress-container {
            margin: 30px 0;
        }

        .progress {
            height: 8px;
            border-radius: 10px;
            background: #e9ecef;
        }

        .progress-bar {
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            display: none;
        }

        .status-message.show {
            display: block;
            animation: slideIn 0.5s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        .admin-form {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        .password-strength {
            margin-top: 10px;
        }

        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            margin-top: 5px;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        @media (max-width: 768px) {
            .deploy-container {
                margin: 10px;
                padding: 10px;
            }

            .deployment-card {
                border-radius: 8px;
            }

            .deploy-header {
                padding: 25px 20px 20px;
            }

            .deploy-header h1 {
                font-size: 1.8rem;
            }

            .deploy-content {
                padding: 20px;
            }

            .step-indicator {
                flex-direction: column;
                padding: 15px;
            }

            .step {
                margin: 8px 0;
            }

            .step-arrow {
                transform: rotate(90deg);
                margin: 5px 0;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .feature-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="deploy-container">
        <div class="deployment-card">
            <div class="deploy-header">
                <h1>E-Voting System Deployment</h1>
                <p class="subtitle">Automated setup and configuration</p>
                <p class="institution">Ogbonnaya Onu Polytechnic</p>
            </div>

            <div class="deploy-content">
                <!-- Step Indicator -->
                <?php if (!$isDeployed): ?>
                <div class="step-indicator">
                    <div class="step">
                        <div class="step-number active" id="step1">1</div>
                        <p class="step-label active">Welcome</p>
                    </div>
                    <div class="step-arrow">→</div>
                    <div class="step">
                        <div class="step-number" id="step2">2</div>
                        <p class="step-label">Database</p>
                    </div>
                    <div class="step-arrow">→</div>
                    <div class="step">
                        <div class="step-number" id="step3">3</div>
                        <p class="step-label">Admin</p>
                    </div>
                    <div class="step-arrow">→</div>
                    <div class="step">
                        <div class="step-number" id="step4">4</div>
                        <p class="step-label">Complete</p>
                    </div>
                </div>
                <?php endif; ?>

            <!-- Step 4: Completion -->
            <div class="deploy-step" id="complete-step">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    </div>
                    <h3 class="text-success mb-4">Deployment Successful!</h3>
                    <p class="text-muted mb-4">Your e-voting system has been successfully deployed and is ready to use.</p>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-home fa-2x text-primary mb-3"></i>
                                    <h5>Student Portal</h5>
                                    <p class="small text-muted">Students can register and vote here</p>
                                    <a href="index.php" class="btn btn-primary">
                                        <i class="fas fa-external-link-alt mr-1"></i>Open Portal
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-cogs fa-2x text-success mb-3"></i>
                                    <h5>Admin Panel</h5>
                                    <p class="small text-muted">Manage elections and view results</p>
                                    <a href="admin/index.php" class="btn btn-success">
                                        <i class="fas fa-external-link-alt mr-1"></i>Open Admin
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-4">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Important:</strong> Please delete or secure the deploy.php file after successful deployment for security reasons.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        let currentStep = 1;
        let deploymentData = {};

        // Show specific step
        function showStep(stepNumber) {
            // Hide all steps
            document.querySelectorAll('.deploy-step').forEach(step => {
                step.classList.remove('active');
            });

            // Update step indicators
            for (let i = 1; i <= 4; i++) {
                const stepElement = document.getElementById(`step${i}`);
                const labelElement = stepElement.parentElement.querySelector('.step-label');

                stepElement.classList.remove('active', 'completed');
                labelElement.classList.remove('active', 'completed');

                if (i < stepNumber) {
                    stepElement.classList.add('completed');
                    labelElement.classList.add('completed');
                } else if (i === stepNumber) {
                    stepElement.classList.add('active');
                    labelElement.classList.add('active');
                }
            }

            // Show current step
            const steps = {
                1: 'welcome-step',
                2: 'database-step',
                3: 'admin-step',
                4: 'complete-step'
            };

            document.getElementById(steps[stepNumber]).classList.add('active');
            currentStep = stepNumber;
        }

        // Start deployment process
        function startDeployment() {
            showStep(2);
            createDatabase();
        }

        // Create database
        function createDatabase() {
            const spinner = document.getElementById('db-spinner');
            const progress = document.getElementById('db-progress');
            const status = document.getElementById('db-status');

            spinner.style.display = 'block';

            // Simulate progress
            let progressValue = 0;
            const progressInterval = setInterval(() => {
                progressValue += Math.random() * 15;
                if (progressValue > 90) progressValue = 90;
                progress.style.width = progressValue + '%';
            }, 200);

            // Make AJAX request to create database
            $.ajax({
                url: 'deploy.php',
                method: 'POST',
                data: { action: 'create_database' },
                dataType: 'json',
                success: function(data) {
                    clearInterval(progressInterval);
                    progress.style.width = '100%';
                    spinner.style.display = 'none';

                    if (data.success) {
                        status.className = 'status-message alert alert-success show';
                        status.innerHTML = '<i class="fas fa-check-circle mr-2"></i>' + data.message;

                        setTimeout(() => {
                            showStep(3);
                        }, 2000);
                    } else {
                        status.className = 'status-message alert alert-danger show';
                        status.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i>' + data.message;
                    }
                },
                error: function(xhr, status, error) {
                    clearInterval(progressInterval);
                    spinner.style.display = 'none';
                    const statusDiv = document.getElementById('db-status');
                    statusDiv.className = 'status-message alert alert-danger show';

                    let errorMessage = 'Connection failed. Please ensure:';
                    errorMessage += '<ul class="mt-2 mb-0">';
                    errorMessage += '<li>XAMPP/WAMP is running</li>';
                    errorMessage += '<li>Apache and MySQL services are started</li>';
                    errorMessage += '<li>You are accessing via localhost (not file://)</li>';
                    errorMessage += '<li>PHP is working correctly</li>';
                    errorMessage += '</ul>';
                    errorMessage += '<p class="mt-2 mb-0"><strong>Error details:</strong> ' + error + '</p>';

                    statusDiv.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i>' + errorMessage;
                }
            });
        }

        // Password strength checker
        function checkPasswordStrength(password) {
            let strength = 0;

            if (password.length >= 8) strength += 25;
            if (password.match(/[A-Z]/)) strength += 25;
            if (password.match(/[a-z]/)) strength += 25;
            if (password.match(/[\d\W]/)) strength += 25;

            return strength;
        }

        // Update password strength indicator
        function updatePasswordStrength() {
            const password = document.getElementById('admin-password').value;
            const strengthText = document.getElementById('strength-text');
            const strengthFill = document.getElementById('strength-fill');

            if (!password) {
                strengthText.textContent = 'Not entered';
                strengthFill.style.width = '0%';
                strengthFill.style.backgroundColor = '#e9ecef';
                return;
            }

            const strength = checkPasswordStrength(password);

            let text, color;
            if (strength < 25) {
                text = 'Very Weak';
                color = '#dc3545';
            } else if (strength < 50) {
                text = 'Weak';
                color = '#fd7e14';
            } else if (strength < 75) {
                text = 'Medium';
                color = '#ffc107';
            } else if (strength < 100) {
                text = 'Strong';
                color = '#20c997';
            } else {
                text = 'Very Strong';
                color = '#28a745';
            }

            strengthText.textContent = text;
            strengthFill.style.width = strength + '%';
            strengthFill.style.backgroundColor = color;
        }

        // Check password match
        function checkPasswordMatch() {
            const password = document.getElementById('admin-password').value;
            const confirm = document.getElementById('admin-confirm').value;
            const matchElement = document.getElementById('password-match');

            if (!confirm) {
                matchElement.textContent = '';
                return false;
            }

            if (password === confirm) {
                matchElement.textContent = '✓ Passwords match';
                matchElement.className = 'form-text text-success';
                return true;
            } else {
                matchElement.textContent = '✗ Passwords do not match';
                matchElement.className = 'form-text text-danger';
                return false;
            }
        }

        // Create admin account
        function createAdminAccount(formData) {
            const submitBtn = document.querySelector('#admin-form button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Account...';

            $.ajax({
                url: 'deploy.php',
                method: 'POST',
                data: {
                    action: 'create_admin',
                    username: formData.get('username'),
                    password: formData.get('password')
                },
                dataType: 'json',
                success: function(data) {
                    if (data.success) {
                        deploymentData.adminCreated = true;
                        deploymentData.adminUsername = formData.get('username');
                        showStep(4);
                    } else {
                        alert('Error creating admin account: ' + data.message);
                    }
                },
                error: function(xhr, status, error) {
                    alert('Error: ' + error);
                },
                complete: function() {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            });
        }

        // Initialize event listeners
        $(document).ready(function() {
            // Password strength monitoring
            $('#admin-password').on('input', updatePasswordStrength);

            // Password match monitoring
            $('#admin-confirm').on('input', checkPasswordMatch);

            // Admin form submission
            $('#admin-form').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const password = formData.get('password');
                const confirm = formData.get('confirm');
                const username = formData.get('username');

                // Validation
                if (!username || username.length < 3) {
                    alert('Username must be at least 3 characters long');
                    return;
                }

                if (!password || password.length < 6) {
                    alert('Password must be at least 6 characters long');
                    return;
                }

                if (password !== confirm) {
                    alert('Passwords do not match');
                    return;
                }

                const strength = checkPasswordStrength(password);
                if (strength < 50) {
                    if (!confirm('Your password is weak. Continue anyway?')) {
                        return;
                    }
                }

                createAdminAccount(formData);
            });
        });
    </script>
</body>
</html>

            <!-- Step 1: Welcome -->
            <div class="deploy-step <?php echo $isDeployed ? '' : 'active'; ?>" id="welcome-step">
                <h2 style="color: #2c3e50; font-weight: 600; margin-bottom: 10px; text-align: center;">Welcome to E-Voting System Deployment</h2>
                <p style="text-align: center; color: #6c757d; margin-bottom: 30px;">This automated setup will configure your complete e-voting system with database, admin account, and all necessary components.</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4><i class="fas fa-cogs"></i>What will be installed</h4>
                        <ul class="feature-list">
                            <li>Complete database structure</li>
                            <li>Student management system</li>
                            <li>Candidate registration</li>
                            <li>Voting mechanism</li>
                            <li>Admin control panel</li>
                            <li>Election settings</li>
                            <li>Security features</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-check-circle"></i>Requirements</h4>
                        <ul class="feature-list">
                            <li>XAMPP/WAMP running</li>
                            <li>MySQL server active</li>
                            <li>PHP 7.4 or higher</li>
                            <li>Web browser</li>
                        </ul>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn-deploy btn-success" onclick="startDeployment()">
                        <i class="fas fa-play" style="margin-right: 8px;"></i>Start Deployment
                    </button>
                </div>
            </div>

            <!-- Step 2: Database Creation -->
            <div class="deploy-step" id="database-step">
                <h3 class="text-center mb-4">Creating Database</h3>
                <div class="progress-container">
                    <div class="progress">
                        <div class="progress-bar bg-primary" id="db-progress" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="loading-spinner" id="db-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-3">Setting up database structure...</p>
                </div>
                <div class="status-message" id="db-status"></div>
            </div>

            <!-- Step 3: Admin Setup -->
            <div class="deploy-step" id="admin-step">
                <h3 class="text-center mb-4">Create Admin Account</h3>
                <p class="text-center text-muted mb-4">Set up your administrator credentials to manage the e-voting system.</p>
                
                <div class="admin-form">
                    <form id="admin-form">
                        <div class="form-group">
                            <label for="admin-username"><i class="fas fa-user mr-2"></i>Admin Username</label>
                            <input type="text" class="form-control" id="admin-username" name="username" required 
                                   placeholder="Enter admin username" value="admin">
                            <small class="form-text text-muted">This will be used to log into the admin panel.</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="admin-password"><i class="fas fa-lock mr-2"></i>Admin Password</label>
                            <input type="password" class="form-control" id="admin-password" name="password" required 
                                   placeholder="Enter secure password">
                            <div class="password-strength">
                                <small class="text-muted">Password strength: <span id="strength-text">Not entered</span></small>
                                <div class="strength-bar">
                                    <div class="strength-fill" id="strength-fill"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="admin-confirm"><i class="fas fa-check mr-2"></i>Confirm Password</label>
                            <input type="password" class="form-control" id="admin-confirm" name="confirm" required 
                                   placeholder="Confirm your password">
                            <small class="form-text" id="password-match"></small>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-deploy">
                                <i class="fas fa-user-shield mr-2"></i>Create Admin Account
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Already Deployed Section -->
            <?php if ($isDeployed): ?>
            <div class="deploy-step active" id="already-deployed">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    </div>
                    <h3 class="text-success mb-4">System Already Deployed!</h3>
                    <p class="text-muted mb-4">Your e-voting system was successfully deployed on <?php echo date('F j, Y \a\t g:i A', strtotime($deploymentData['completed_at'] ?? '')); ?>.</p>
                    
                    <?php if (isset($deploymentData['admin_username'])): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-user-shield mr-2"></i>
                        <strong>Admin Username:</strong> <?php echo htmlspecialchars($deploymentData['admin_username']); ?>
                    </div>
                    <?php endif; ?>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-home fa-2x text-primary mb-3"></i>
                                    <h5>Student Portal</h5>
                                    <p class="small text-muted">Students can register and vote here</p>
                                    <a href="index.php" class="btn btn-primary">
                                        <i class="fas fa-external-link-alt mr-1"></i>Open Portal
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-cogs fa-2x text-success mb-3"></i>
                                    <h5>Admin Panel</h5>
                                    <p class="small text-muted">Manage elections and view results</p>
                                    <a href="admin/index.php" class="btn btn-success">
                                        <i class="fas fa-external-link-alt mr-1"></i>Open Admin
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning mt-4">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>Security Note:</strong> Consider deleting this deployment file (deploy.php) for security reasons.
                    </div>
                </div>
            </div>
            <?php endif; ?>
