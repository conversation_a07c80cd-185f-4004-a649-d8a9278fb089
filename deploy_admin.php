<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Database configuration
$host = "localhost";
$username = "root";
$password = "";
$database = "voting_system";

// Response function
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Get request data
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['action'])) {
    sendResponse(false, 'Invalid request');
}

if ($input['action'] === 'create_admin') {
    try {
        // Validate input
        if (!isset($input['username']) || !isset($input['password'])) {
            throw new Exception('Username and password are required');
        }

        $admin_username = trim($input['username']);
        $admin_password = $input['password'];

        // Validation
        if (empty($admin_username) || strlen($admin_username) < 3) {
            throw new Exception('Username must be at least 3 characters long');
        }

        if (empty($admin_password) || strlen($admin_password) < 6) {
            throw new Exception('Password must be at least 6 characters long');
        }

        // Sanitize username
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $admin_username)) {
            throw new Exception('Username can only contain letters, numbers, and underscores');
        }

        // Connect to database
        $conn = new mysqli($host, $username, $password, $database);

        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }

        // Check if database exists
        $result = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$database'");
        if ($result->num_rows == 0) {
            throw new Exception("Database '$database' does not exist. Please run database deployment first.");
        }

        // Check if admins table exists
        $result = $conn->query("SHOW TABLES LIKE 'admins'");
        if ($result->num_rows == 0) {
            throw new Exception("Admins table does not exist. Please run database deployment first.");
        }

        // Check if admin already exists
        $stmt = $conn->prepare("SELECT id FROM admins WHERE username = ?");
        $stmt->bind_param("s", $admin_username);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            // Update existing admin
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("UPDATE admins SET password = ? WHERE username = ?");
            $stmt->bind_param("ss", $hashed_password, $admin_username);
            
            if (!$stmt->execute()) {
                throw new Exception("Failed to update admin account");
            }
            
            $message = "Admin account updated successfully";
        } else {
            // Create new admin
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO admins (username, password) VALUES (?, ?)");
            $stmt->bind_param("ss", $admin_username, $hashed_password);
            
            if (!$stmt->execute()) {
                throw new Exception("Failed to create admin account");
            }
            
            $message = "Admin account created successfully";
        }

        // Update deployment status
        $deploymentStatusFile = 'deployment_status.json';
        $deploymentStatus = [];
        
        if (file_exists($deploymentStatusFile)) {
            $deploymentStatus = json_decode(file_get_contents($deploymentStatusFile), true);
        }
        
        $deploymentStatus['admin_created'] = true;
        $deploymentStatus['admin_username'] = $admin_username;
        $deploymentStatus['admin_created_at'] = date('Y-m-d H:i:s');
        $deploymentStatus['deployment_completed'] = true;
        $deploymentStatus['completed_at'] = date('Y-m-d H:i:s');
        
        file_put_contents($deploymentStatusFile, json_encode($deploymentStatus, JSON_PRETTY_PRINT));

        // Log the admin creation
        $logEntry = [
            'action' => 'admin_created',
            'username' => $admin_username,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        $logFile = 'deployment_log.json';
        $logs = [];
        
        if (file_exists($logFile)) {
            $logs = json_decode(file_get_contents($logFile), true) ?? [];
        }
        
        $logs[] = $logEntry;
        file_put_contents($logFile, json_encode($logs, JSON_PRETTY_PRINT));

        $conn->close();

        sendResponse(true, $message, [
            'username' => $admin_username,
            'deployment_completed' => true
        ]);

    } catch (Exception $e) {
        sendResponse(false, $e->getMessage());
    }
} else {
    sendResponse(false, 'Invalid action');
}
?>
