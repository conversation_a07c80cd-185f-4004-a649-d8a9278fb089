@echo off
title E-Voting System Deployment
color 0A

echo.
echo ========================================
echo   E-VOTING SYSTEM DEPLOYMENT LAUNCHER
echo   Ogbonnaya Onu Polytechnic
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Running with administrator privileges...
) else (
    echo [WARNING] Not running as administrator. Some operations may fail.
    echo [INFO] Right-click and "Run as administrator" for best results.
    echo.
)

:: Set variables
set XAMPP_PATH=C:\xampp
set PROJECT_PATH=%~dp0
set PROJECT_NAME=VOTE

echo [INFO] Project location: %PROJECT_PATH%
echo [INFO] Looking for XAMPP at: %XAMPP_PATH%
echo.

:: Check if XAMPP exists
if not exist "%XAMPP_PATH%" (
    echo [ERROR] XAMPP not found at %XAMPP_PATH%
    echo [INFO] Please install XAMPP or update the XAMPP_PATH in this script
    echo [INFO] Download XAMPP from: https://www.apachefriends.org/
    pause
    exit /b 1
)

:: Check if Apache and MySQL executables exist
if not exist "%XAMPP_PATH%\apache\bin\httpd.exe" (
    echo [ERROR] Apache not found in XAMPP installation
    pause
    exit /b 1
)

if not exist "%XAMPP_PATH%\mysql\bin\mysqld.exe" (
    echo [ERROR] MySQL not found in XAMPP installation
    pause
    exit /b 1
)

echo [INFO] XAMPP installation verified successfully
echo.

:: Check if services are already running
echo [INFO] Checking service status...
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [INFO] Apache is already running
    set APACHE_RUNNING=1
) else (
    echo [INFO] Apache is not running
    set APACHE_RUNNING=0
)

tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [INFO] MySQL is already running
    set MYSQL_RUNNING=1
) else (
    echo [INFO] MySQL is not running
    set MYSQL_RUNNING=0
)

echo.

:: Start Apache if not running
if %APACHE_RUNNING%==0 (
    echo [INFO] Starting Apache web server...
    start "" "%XAMPP_PATH%\apache\bin\httpd.exe"
    timeout /t 3 /nobreak >nul
    
    :: Verify Apache started
    tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
    if "%ERRORLEVEL%"=="0" (
        echo [SUCCESS] Apache started successfully
    ) else (
        echo [ERROR] Failed to start Apache
        echo [INFO] Please check XAMPP configuration or start manually
        pause
        exit /b 1
    )
) else (
    echo [INFO] Apache already running, skipping start
)

:: Start MySQL if not running
if %MYSQL_RUNNING%==0 (
    echo [INFO] Starting MySQL database server...
    start "" "%XAMPP_PATH%\mysql\bin\mysqld.exe" --defaults-file="%XAMPP_PATH%\mysql\bin\my.ini"
    timeout /t 5 /nobreak >nul
    
    :: Verify MySQL started
    tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
    if "%ERRORLEVEL%"=="0" (
        echo [SUCCESS] MySQL started successfully
    ) else (
        echo [ERROR] Failed to start MySQL
        echo [INFO] Please check XAMPP configuration or start manually
        pause
        exit /b 1
    )
) else (
    echo [INFO] MySQL already running, skipping start
)

echo.
echo [INFO] Waiting for services to fully initialize...
timeout /t 3 /nobreak >nul

:: Check if project files exist in htdocs
set HTDOCS_PROJECT=%XAMPP_PATH%\htdocs\%PROJECT_NAME%
if not exist "%HTDOCS_PROJECT%" (
    echo [INFO] Project not found in htdocs, copying files...
    mkdir "%HTDOCS_PROJECT%" 2>nul
    
    :: Copy all files except this batch file
    for %%f in ("%PROJECT_PATH%\*") do (
        if /I not "%%~nxf"=="Deploy_E-Voting_System.bat" (
            copy "%%f" "%HTDOCS_PROJECT%\" >nul 2>&1
        )
    )
    
    :: Copy subdirectories
    for /D %%d in ("%PROJECT_PATH%\*") do (
        xcopy "%%d" "%HTDOCS_PROJECT%\%%~nxd\" /E /I /Q >nul 2>&1
    )
    
    echo [SUCCESS] Project files copied to htdocs
) else (
    echo [INFO] Project already exists in htdocs
    echo [INFO] Updating files...
    
    :: Update files (excluding batch file)
    for %%f in ("%PROJECT_PATH%\*") do (
        if /I not "%%~nxf"=="Deploy_E-Voting_System.bat" (
            copy "%%f" "%HTDOCS_PROJECT%\" >nul 2>&1
        )
    )
    echo [SUCCESS] Project files updated
)

echo.
echo [INFO] Testing web server connection...

:: Test if localhost is accessible
ping -n 1 127.0.0.1 >nul 2>&1
if %errorlevel%==0 (
    echo [SUCCESS] Localhost connection available
) else (
    echo [ERROR] Cannot reach localhost
    pause
    exit /b 1
)

:: Construct the deployment URL
set DEPLOY_URL=http://localhost/%PROJECT_NAME%/deploy.php

echo.
echo ========================================
echo   DEPLOYMENT READY!
echo ========================================
echo.
echo [INFO] Services Status:
echo   - Apache Web Server: RUNNING
echo   - MySQL Database: RUNNING
echo   - Project Location: %HTDOCS_PROJECT%
echo.
echo [INFO] Opening deployment interface...
echo [INFO] URL: %DEPLOY_URL%
echo.

:: Open the deployment page in default browser
start "" "%DEPLOY_URL%"

echo [SUCCESS] Deployment interface launched!
echo.
echo [INFO] The deployment page should open in your browser.
echo [INFO] If it doesn't open automatically, copy this URL:
echo [INFO] %DEPLOY_URL%
echo.
echo [INFO] After successful deployment, you can access:
echo   - Student Portal: http://localhost/%PROJECT_NAME%/
echo   - Admin Panel: http://localhost/%PROJECT_NAME%/admin/
echo.
echo [INFO] Press any key to close this window...
pause >nul

:: Optional: Ask if user wants to stop services
echo.
echo [QUESTION] Do you want to stop XAMPP services? (Y/N)
set /p STOP_SERVICES=
if /I "%STOP_SERVICES%"=="Y" (
    echo [INFO] Stopping services...
    taskkill /F /IM httpd.exe >nul 2>&1
    taskkill /F /IM mysqld.exe >nul 2>&1
    echo [INFO] Services stopped
)

echo.
echo [INFO] Deployment launcher finished. Thank you!
pause
