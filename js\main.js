// Main JavaScript file for Ogbonnaya Onu E-Voting System

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Handle voting form submission
    const voteForm = document.getElementById('vote-form');
    if (voteForm) {
        voteForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Check if all positions have been voted for
            const positions = document.querySelectorAll('.position-section');
            let allSelected = true;

            positions.forEach(position => {
                const positionId = position.dataset.positionId;
                const selectedCandidate = document.querySelector(`input[name="candidate_${positionId}"]:checked`);

                if (!selectedCandidate) {
                    allSelected = false;
                    const errorMsg = document.createElement('div');
                    errorMsg.className = 'alert alert-danger mt-2';
                    errorMsg.textContent = 'Please select a candidate for this position.';

                    // Remove any existing error messages
                    const existingError = position.querySelector('.alert');
                    if (existingError) {
                        existingError.remove();
                    }

                    position.appendChild(errorMsg);
                }
            });

            if (allSelected) {
                // Submit the form via AJAX
                const formData = new FormData(voteForm);

                fetch('api/vote.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message and redirect
                        document.querySelector('.voting-container').innerHTML = `
                            <div class="text-center">
                                <img src="images/voted.png" alt="Vote Success" class="img-fluid mb-3" style="max-width: 200px;">
                                <h2 class="text-success">Congratulations!</h2>
                                <p class="lead">Your vote has been successfully recorded.</p>
                                <p>You will be redirected to the results page in <span id="countdown">5</span> seconds.</p>
                            </div>
                        `;

                        // Countdown and redirect
                        let seconds = 5;
                        const countdownEl = document.getElementById('countdown');
                        const interval = setInterval(() => {
                            seconds--;
                            countdownEl.textContent = seconds;
                            if (seconds <= 0) {
                                clearInterval(interval);
                                window.location.href = 'results.php';
                            }
                        }, 1000);
                    } else {
                        // Show error message
                        const errorContainer = document.getElementById('error-container');
                        errorContainer.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                        errorContainer.scrollIntoView({ behavior: 'smooth' });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    const errorContainer = document.getElementById('error-container');
                    errorContainer.innerHTML = '<div class="alert alert-danger">An error occurred. Please try again later.</div>';
                    errorContainer.scrollIntoView({ behavior: 'smooth' });
                });
            }
        });
    }

    // Initialize results charts if on results page
    initializeResultsCharts();

    // Set up real-time updates for results page
    setupRealTimeUpdates();
});

// Function to initialize charts on the results page
function initializeResultsCharts() {
    const chartContainers = document.querySelectorAll('.chart-container');

    if (chartContainers.length > 0) {
        // Fetch initial results data
        fetchResultsData();

        // Update results every 5 seconds
        setInterval(fetchResultsData, 5000);
    }
}

// Function to fetch results data and update charts
function fetchResultsData() {
    fetch('api/results.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateResultsCharts(data.results);
                updateVoteCounts(data.results);
            }
        })
        .catch(error => console.error('Error fetching results:', error));
}

// Function to update the charts with new data
function updateResultsCharts(results) {
    results.forEach(position => {
        const chartCanvas = document.getElementById(`chart-${position.position_id}`);

        if (chartCanvas) {
            // Check if chart already exists
            const chartInstance = Chart.getChart(chartCanvas);

            // Prepare data for chart
            const labels = position.candidates.map(candidate => candidate.name);
            const votes = position.candidates.map(candidate => candidate.votes);
            const backgroundColors = generateColors(position.candidates.length);

            const chartData = {
                labels: labels,
                datasets: [{
                    label: 'Votes',
                    data: votes,
                    backgroundColor: backgroundColors,
                    borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                    borderWidth: 1
                }]
            };

            const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = votes.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${label}: ${value} votes (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            };

            // Update or create chart
            if (chartInstance) {
                chartInstance.data = chartData;
                chartInstance.update();
            } else {
                new Chart(chartCanvas, {
                    type: 'bar',
                    data: chartData,
                    options: chartOptions
                });
            }
        }
    });
}

// Function to update vote counts in the DOM
function updateVoteCounts(results) {
    results.forEach(position => {
        position.candidates.forEach(candidate => {
            const countElement = document.getElementById(`vote-count-${candidate.id}`);
            if (countElement) {
                // Add animation class if the value has changed
                const currentValue = parseInt(countElement.textContent);
                if (currentValue !== candidate.votes) {
                    countElement.classList.add('real-time-update');
                    setTimeout(() => {
                        countElement.classList.remove('real-time-update');
                    }, 2000);
                }

                countElement.textContent = candidate.votes;

                // Update percentage
                const percentElement = document.getElementById(`vote-percent-${candidate.id}`);
                if (percentElement) {
                    const total = position.candidates.reduce((sum, c) => sum + c.votes, 0);
                    const percentage = total > 0 ? Math.round((candidate.votes / total) * 100) : 0;
                    percentElement.textContent = `${percentage}%`;
                }
            }
        });
    });
}

// Function to set up WebSocket connection for real-time updates
function setupRealTimeUpdates() {
    const resultsContainer = document.querySelector('.results-container');

    if (resultsContainer) {
        // Check if browser supports WebSockets
        if ('WebSocket' in window) {
            // Connect to WebSocket server
            const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
            const wsUrl = `${protocol}${window.location.host}/websocket/server.php`;

            try {
                const socket = new WebSocket(wsUrl);

                socket.onopen = function() {
                    console.log('WebSocket connection established');
                };

                socket.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    if (data.type === 'vote_update') {
                        // Update results with the new data
                        updateResultsCharts(data.results);
                        updateVoteCounts(data.results);
                    }
                };

                socket.onclose = function() {
                    console.log('WebSocket connection closed');
                    // Try to reconnect after 5 seconds
                    setTimeout(setupRealTimeUpdates, 5000);
                };

                socket.onerror = function(error) {
                    console.error('WebSocket error:', error);
                    // Fall back to polling if WebSocket fails
                    console.log('Falling back to polling for updates');
                };
            } catch (error) {
                console.error('WebSocket connection error:', error);
                console.log('Using polling for updates instead');
            }
        } else {
            console.log('WebSocket not supported by this browser, using polling instead');
        }
    }
}

// Function to generate random colors for charts
function generateColors(count) {
    const colors = [
        'rgba(54, 162, 235, 0.7)',
        'rgba(255, 99, 132, 0.7)',
        'rgba(75, 192, 192, 0.7)',
        'rgba(255, 159, 64, 0.7)',
        'rgba(153, 102, 255, 0.7)',
        'rgba(255, 205, 86, 0.7)',
        'rgba(201, 203, 207, 0.7)',
        'rgba(255, 99, 71, 0.7)',
        'rgba(46, 139, 87, 0.7)',
        'rgba(106, 90, 205, 0.7)'
    ];

    // If we need more colors than available, generate random ones
    if (count > colors.length) {
        for (let i = colors.length; i < count; i++) {
            const r = Math.floor(Math.random() * 255);
            const g = Math.floor(Math.random() * 255);
            const b = Math.floor(Math.random() * 255);
            colors.push(`rgba(${r}, ${g}, ${b}, 0.7)`);
        }
    }

    return colors.slice(0, count);
}


