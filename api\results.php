<?php
// Set content type to JSON
header('Content-Type: application/json');

// Include database connection
$conn = require_once '../database/db_config.php';

// Get positions and candidates with vote counts
$positions = [];
$sql = "SELECT id, position_name FROM positions ORDER BY id";
$positions_result = $conn->query($sql);

while ($position = $positions_result->fetch_assoc()) {
    $position_id = $position['id'];
    $candidates = [];
    
    $sql = "SELECT c.id, c.full_name as name, c.program, c.photo, COUNT(v.id) as votes
            FROM candidates c
            LEFT JOIN votes v ON c.id = v.candidate_id
            WHERE c.position_id = $position_id
            GROUP BY c.id
            ORDER BY votes DESC";
    
    $candidates_result = $conn->query($sql);
    
    while ($candidate = $candidates_result->fetch_assoc()) {
        // Convert votes to integer
        $candidate['votes'] = (int)$candidate['votes'];
        $candidates[] = $candidate;
    }
    
    $positions[] = [
        'position_id' => $position_id,
        'position_name' => $position['position_name'],
        'candidates' => $candidates
    ];
}

// Get total votes
$sql = "SELECT COUNT(*) as total FROM votes";
$result = $conn->query($sql);
$total_votes = (int)$result->fetch_assoc()['total'];

// Get total registered students
$sql = "SELECT COUNT(*) as total FROM students";
$result = $conn->query($sql);
$total_students = (int)$result->fetch_assoc()['total'];

// Calculate voter turnout
$voter_turnout = $total_students > 0 ? round(($total_votes / $total_students) * 100) : 0;

// Return results as JSON
echo json_encode([
    'success' => true,
    'results' => $positions,
    'stats' => [
        'total_votes' => $total_votes,
        'total_students' => $total_students,
        'voter_turnout' => $voter_turnout
    ]
]);

// Close connection
$conn->close();
?>
