<?php
// Start session
session_start();

// Redirect if not logged in
if (!isset($_SESSION['student_id'])) {
    header('Location: login.php');
    exit;
}

// Include database connection and functions
$conn = require_once 'database/db_config.php';
require_once 'includes/functions.php';

// Check if election is active
$election_active = isElectionActive();

// Check if student has already voted
$student_id = $_SESSION['student_id'];
$stmt = $conn->prepare("SELECT has_voted FROM students WHERE id = ?");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

$already_voted = $student['has_voted'];

// Get positions and candidates
$positions = [];
$sql = "SELECT id, position_name FROM positions ORDER BY id";
$positions_result = $conn->query($sql);

while ($position = $positions_result->fetch_assoc()) {
    $position_id = $position['id'];
    $candidates = [];

    $sql = "SELECT id, full_name, program, photo FROM candidates WHERE position_id = $position_id";
    $candidates_result = $conn->query($sql);

    while ($candidate = $candidates_result->fetch_assoc()) {
        $candidates[] = $candidate;
    }

    $positions[] = [
        'id' => $position['id'],
        'name' => $position['position_name'],
        'candidates' => $candidates
    ];
}

// Include header
include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Student Union Government Elections</h2>
    <div class="text-right">
        <h5 class="text-primary">Welcome, <?php echo isset($_SESSION['student_first_name']) ? htmlspecialchars($_SESSION['student_first_name']) : htmlspecialchars($_SESSION['student_name']); ?>!</h5>
    </div>
</div>

<div id="error-container"></div>

<?php if (!$election_active): ?>
    <div class="text-center py-5 my-5">
        <h3 class="text-warning mb-4">The election is not currently active</h3>
        <p class="lead mb-4">Please check back later when the election has started.</p>
        <a href="index.php" class="btn btn-primary mt-3">Go Back to Home</a>
    </div>
<?php elseif ($already_voted): ?>
    <div class="text-center">
        <img src="images/voted.png" alt="Already Voted" class="img-fluid mb-3" style="max-width: 200px;">
        <h3 class="text-success">You have already cast your vote!</h3>
        <p class="lead">Thank you for participating in the election.</p>
        <a href="results.php" class="btn btn-primary mt-3">View Results</a>
    </div>
<?php else: ?>
    <div class="voting-container">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> Please select one candidate for each position and submit your vote.
        </div>

        <form id="vote-form" method="post">
            <?php foreach ($positions as $position): ?>
                <div class="card mb-4 position-section" data-position-id="<?php echo $position['id']; ?>">
                    <div class="card-header">
                        <h4 class="mb-0"><?php echo $position['name']; ?></h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($position['candidates'] as $candidate): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card candidate-card">
                                        <label for="candidate_<?php echo $candidate['id']; ?>" class="w-100">
                                            <img src="<?php echo $candidate['photo']; ?>" alt="<?php echo $candidate['full_name']; ?>" class="candidate-photo">
                                            <h5><?php echo $candidate['full_name']; ?></h5>
                                            <p class="text-muted">Program: <?php echo $candidate['program']; ?></p>
                                            <div class="vote-option mt-2">
                                                <input type="radio" name="candidate_<?php echo $position['id']; ?>" id="candidate_<?php echo $candidate['id']; ?>" value="<?php echo $candidate['id']; ?>" required>
                                                <label for="candidate_<?php echo $candidate['id']; ?>">Select</label>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

            <div class="text-center mb-4">
                <button type="submit" class="btn btn-primary btn-lg">Submit Vote</button>
            </div>
        </form>
    </div>
<?php endif; ?>

<?php
// Include footer
include 'includes/footer.php';
?>
