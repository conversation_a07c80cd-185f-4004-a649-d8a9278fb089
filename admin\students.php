<?php
// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection
$conn = require_once '../database/db_config.php';

// Check if approval_status column exists
$column_check = $conn->query("SHOW COLUMNS FROM students LIKE 'approval_status'");
if ($column_check->num_rows == 0) {
    // Add the column if it doesn't exist
    $conn->query("ALTER TABLE students ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending' AFTER has_voted");
    $conn->query("UPDATE students SET approval_status = 'approved'");
}

// Handle search
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';

// Prepare query based on search and filter
$query = "SELECT id, full_name, matric_no, department, level, has_voted, approval_status, created_at FROM students";
$where_clauses = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_clauses[] = "(full_name LIKE ? OR matric_no LIKE ? OR department LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'sss';
}

if ($filter === 'voted') {
    $where_clauses[] = "has_voted = 1";
} elseif ($filter === 'not_voted') {
    $where_clauses[] = "has_voted = 0";
} elseif ($filter === 'approved') {
    $where_clauses[] = "approval_status = 'approved'";
} elseif ($filter === 'pending') {
    $where_clauses[] = "approval_status = 'pending'";
} elseif ($filter === 'rejected') {
    $where_clauses[] = "approval_status = 'rejected'";
}

if (!empty($where_clauses)) {
    $query .= " WHERE " . implode(' AND ', $where_clauses);
}

$query .= " ORDER BY created_at DESC";

// Prepare and execute statement
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
$students = [];

while ($student = $result->fetch_assoc()) {
    $students[] = $student;
}

// Get student voting details if requested
$student_votes = [];
if (isset($_GET['view_votes']) && is_numeric($_GET['view_votes'])) {
    $student_id = $_GET['view_votes'];

    $sql = "SELECT v.id, c.full_name as candidate_name, p.position_name, v.created_at
            FROM votes v
            JOIN candidates c ON v.candidate_id = c.id
            JOIN positions p ON v.position_id = p.id
            WHERE v.student_id = ?
            ORDER BY p.id";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $votes_result = $stmt->get_result();

    while ($vote = $votes_result->fetch_assoc()) {
        $student_votes[] = $vote;
    }

    // Get student details
    $sql = "SELECT full_name, matric_no FROM students WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $student_result = $stmt->get_result();
    $student_details = $student_result->fetch_assoc();
}

// Include admin header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main content -->
        <main class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
            <h1 class="h2 mb-4">View Students</h1>

            <!-- Search and filter form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" action="students.php" class="form-inline">
                        <div class="form-group mr-2 mb-2">
                            <input type="text" class="form-control" name="search" placeholder="Search by name, matric no, or department" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="form-group mr-2 mb-2">
                            <select class="form-control" name="filter">
                                <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Students</option>
                                <option value="voted" <?php echo $filter === 'voted' ? 'selected' : ''; ?>>Voted</option>
                                <option value="not_voted" <?php echo $filter === 'not_voted' ? 'selected' : ''; ?>>Not Voted</option>
                                <option value="approved" <?php echo $filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                <option value="pending" <?php echo $filter === 'pending' ? 'selected' : ''; ?>>Pending Approval</option>
                                <option value="rejected" <?php echo $filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary mb-2">Apply</button>
                        <?php if (!empty($search) || $filter !== 'all'): ?>
                            <a href="students.php" class="btn btn-secondary mb-2 ml-2">Clear</a>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <!-- Student votes modal -->
            <?php if (isset($_GET['view_votes']) && !empty($student_votes)): ?>
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Votes by <?php echo $student_details['full_name']; ?> (<?php echo $student_details['matric_no']; ?>)</h5>
                        <a href="students.php<?php echo !empty($search) || $filter !== 'all' ? "?search=$search&filter=$filter" : ''; ?>" class="btn btn-sm btn-secondary">
                            <i class="fas fa-times"></i> Close
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Position</th>
                                        <th>Candidate</th>
                                        <th>Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($student_votes as $vote): ?>
                                        <tr>
                                            <td><?php echo $vote['position_name']; ?></td>
                                            <td><?php echo $vote['candidate_name']; ?></td>
                                            <td><?php echo date('M d, Y H:i', strtotime($vote['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Students list -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Registered Students</h5>
                </div>
                <div class="card-body">
                    <?php if (count($students) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Matric No</th>
                                        <th>Department</th>
                                        <th>Level</th>
                                        <th>Approval Status</th>
                                        <th>Voting Status</th>
                                        <th>Registration Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student): ?>
                                        <tr>
                                            <td><?php echo $student['full_name']; ?></td>
                                            <td><?php echo $student['matric_no']; ?></td>
                                            <td><?php echo $student['department']; ?></td>
                                            <td><?php echo $student['level']; ?></td>
                                            <td>
                                                <?php if ($student['approval_status'] === 'approved'): ?>
                                                    <span class="badge badge-success">Approved</span>
                                                <?php elseif ($student['approval_status'] === 'pending'): ?>
                                                    <span class="badge badge-warning">Pending</span>
                                                <?php elseif ($student['approval_status'] === 'rejected'): ?>
                                                    <span class="badge badge-danger">Rejected</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($student['has_voted']): ?>
                                                    <span class="badge badge-success">Voted</span>
                                                <?php else: ?>
                                                    <span class="badge badge-warning">Not Voted</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($student['created_at'])); ?></td>
                                            <td>
                                                <?php if ($student['has_voted']): ?>
                                                    <a href="students.php?view_votes=<?php echo $student['id']; ?><?php echo !empty($search) || $filter !== 'all' ? "&search=$search&filter=$filter" : ''; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i> View Votes
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">No votes</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-center">No students found matching your criteria.</p>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
// Include admin footer
include 'includes/footer.php';
?>
