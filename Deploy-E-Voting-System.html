<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deploy E-Voting System - Ogbonnaya Onu Polytechnic</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            width: 100%;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px 30px;
        }

        .step {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .step.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .step-item {
            display: flex;
            align-items: center;
            margin: 10px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .step-number.active {
            background: #007bff;
            color: white;
        }

        .step-number.completed {
            background: #28a745;
            color: white;
        }

        .arrow {
            color: #dee2e6;
            margin: 0 10px;
            font-size: 1.2rem;
        }

        .btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: bold;
            border-radius: 50px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(40,167,69,0.3);
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(40,167,69,0.4);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            box-shadow: 0 8px 20px rgba(108,117,125,0.3);
        }

        .btn-secondary:hover {
            box-shadow: 0 12px 25px rgba(108,117,125,0.4);
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
            position: relative;
            padding-left: 30px;
        }

        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid;
        }

        .alert-info {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }

        .alert-success {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .alert-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        .text-center {
            text-align: center;
        }

        .text-muted {
            color: #6c757d;
        }

        .mb-4 {
            margin-bottom: 2rem;
        }

        .mt-4 {
            margin-top: 2rem;
        }

        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -10px;
        }

        .col {
            flex: 1;
            padding: 10px;
            min-width: 250px;
        }

        .card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 10px 0;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .success-icon {
            color: #28a745;
        }

        .info-icon {
            color: #007bff;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, #007bff, #0056b3);
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
            width: 0%;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .step-indicator {
                flex-direction: column;
                align-items: center;
            }
            
            .arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }
            
            .row {
                flex-direction: column;
            }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Deploy E-Voting System</h1>
            <p>Ogbonnaya Onu Polytechnic - One-Click Setup</p>
        </div>

        <div class="content">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step-item">
                    <div class="step-number active" id="step1">1</div>
                    <span>Start</span>
                </div>
                <div class="arrow">→</div>
                <div class="step-item">
                    <div class="step-number" id="step2">2</div>
                    <span>Setup</span>
                </div>
                <div class="arrow">→</div>
                <div class="step-item">
                    <div class="step-number" id="step3">3</div>
                    <span>Deploy</span>
                </div>
                <div class="arrow">→</div>
                <div class="step-item">
                    <div class="step-number" id="step4">4</div>
                    <span>Complete</span>
                </div>
            </div>

            <!-- Step 1: Welcome -->
            <div class="step active" id="welcome-step">
                <h2 class="text-center mb-4">Welcome to E-Voting System Deployment</h2>
                <p class="text-center text-muted mb-4">This will automatically set up your complete e-voting system with just a few clicks.</p>
                
                <div class="row">
                    <div class="col">
                        <div class="card">
                            <h4>🎯 What This Will Do:</h4>
                            <ul class="feature-list">
                                <li>Start XAMPP services automatically</li>
                                <li>Create voting system database</li>
                                <li>Set up all required tables</li>
                                <li>Create admin account</li>
                                <li>Launch the system</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card">
                            <h4>📋 Requirements:</h4>
                            <ul class="feature-list">
                                <li>XAMPP installed on your computer</li>
                                <li>Windows operating system</li>
                                <li>Internet connection (for resources)</li>
                                <li>Modern web browser</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button class="btn" onclick="startDeployment()">
                        🚀 Start One-Click Deployment
                    </button>
                </div>
            </div>

            <!-- Step 2: XAMPP Setup -->
            <div class="step" id="setup-step">
                <h2 class="text-center mb-4">Setting Up XAMPP</h2>
                <div class="text-center">
                    <div class="spinner"></div>
                    <p>Starting XAMPP services...</p>
                    <div class="progress">
                        <div class="progress-bar" id="setup-progress"></div>
                    </div>
                </div>
                <div id="setup-status"></div>
            </div>

            <!-- Step 3: Database Creation -->
            <div class="step" id="deploy-step">
                <h2 class="text-center mb-4">Creating Database & Admin Account</h2>
                
                <div class="alert alert-info">
                    <strong>📝 Admin Account Setup</strong><br>
                    Create your administrator credentials to manage the e-voting system.
                </div>

                <div class="form-group">
                    <label for="admin-username">👤 Admin Username:</label>
                    <input type="text" id="admin-username" class="form-control" value="admin" placeholder="Enter admin username">
                </div>

                <div class="form-group">
                    <label for="admin-password">🔒 Admin Password:</label>
                    <input type="password" id="admin-password" class="form-control" placeholder="Enter secure password">
                    <small class="text-muted">Minimum 6 characters recommended</small>
                </div>

                <div class="form-group">
                    <label for="admin-confirm">✅ Confirm Password:</label>
                    <input type="password" id="admin-confirm" class="form-control" placeholder="Confirm your password">
                </div>

                <div class="text-center mt-4">
                    <button class="btn" onclick="createSystem()">
                        🎯 Create E-Voting System
                    </button>
                </div>

                <div id="deploy-status" class="mt-4"></div>
            </div>

            <!-- Step 4: Complete -->
            <div class="step" id="complete-step">
                <div class="text-center">
                    <div class="icon success-icon">✅</div>
                    <h2 class="mb-4">Deployment Successful!</h2>
                    <p class="text-muted mb-4">Your e-voting system has been successfully deployed and is ready to use.</p>
                    
                    <div class="row">
                        <div class="col">
                            <div class="card">
                                <div class="icon info-icon">🏠</div>
                                <h4>Student Portal</h4>
                                <p class="text-muted">Students can register and vote here</p>
                                <a href="#" class="btn" id="student-portal-link">Open Student Portal</a>
                            </div>
                        </div>
                        <div class="col">
                            <div class="card">
                                <div class="icon success-icon">⚙️</div>
                                <h4>Admin Panel</h4>
                                <p class="text-muted">Manage elections and view results</p>
                                <a href="#" class="btn" id="admin-panel-link">Open Admin Panel</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning mt-4">
                        <strong>🔒 Security Note:</strong> For security reasons, consider deleting this deployment file after successful setup.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let deploymentData = {};

        // Show specific step
        function showStep(stepNumber) {
            // Hide all steps
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('active');
            });

            // Update step indicators
            for (let i = 1; i <= 4; i++) {
                const stepElement = document.getElementById(`step${i}`);
                stepElement.classList.remove('active', 'completed');

                if (i < stepNumber) {
                    stepElement.classList.add('completed');
                } else if (i === stepNumber) {
                    stepElement.classList.add('active');
                }
            }

            // Show current step
            const steps = {
                1: 'welcome-step',
                2: 'setup-step',
                3: 'deploy-step',
                4: 'complete-step'
            };

            document.getElementById(steps[stepNumber]).classList.add('active');
            currentStep = stepNumber;
        }

        // Start deployment process
        function startDeployment() {
            showStep(2);
            setupXAMPP();
        }

        // Setup XAMPP (simulated)
        function setupXAMPP() {
            const progressBar = document.getElementById('setup-progress');
            const statusDiv = document.getElementById('setup-status');

            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;

                progressBar.style.width = progress + '%';

                if (progress >= 100) {
                    clearInterval(interval);

                    // Create batch file content for XAMPP startup
                    const batchContent = `@echo off
title XAMPP Startup for E-Voting System
echo Starting XAMPP services for E-Voting System...
echo.

:: Check if XAMPP exists
if not exist "C:\\xampp\\xampp-control.exe" (
    echo XAMPP not found at C:\\xampp\\
    echo Please install XAMPP from https://www.apachefriends.org/
    pause
    exit
)

:: Start XAMPP Control Panel
start "" "C:\\xampp\\xampp-control.exe"

:: Wait a moment
timeout /t 3 /nobreak >nul

:: Open localhost to verify
start "" "http://localhost/"

echo.
echo XAMPP Control Panel opened!
echo Please start Apache and MySQL services manually.
echo Then return to the deployment page.
echo.
pause`;

                    // Create and download the batch file
                    const blob = new Blob([batchContent], { type: 'text/plain' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'Start-XAMPP-for-E-Voting.bat';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    statusDiv.innerHTML = `
                        <div class="alert alert-success">
                            <strong>✅ XAMPP Startup File Created!</strong><br>
                            A batch file has been downloaded to start XAMPP.<br><br>
                            <strong>Next Steps:</strong><br>
                            1. Run the downloaded "Start-XAMPP-for-E-Voting.bat" file<br>
                            2. In XAMPP Control Panel, start Apache and MySQL<br>
                            3. Return here and continue to the next step
                        </div>
                        <div class="text-center">
                            <button class="btn" onclick="showStep(3)">✅ XAMPP Started - Continue</button>
                        </div>
                    `;
                }
            }, 100);
        }

        // Create system (simulated database and admin creation)
        function createSystem() {
            const username = document.getElementById('admin-username').value.trim();
            const password = document.getElementById('admin-password').value;
            const confirm = document.getElementById('admin-confirm').value;
            const statusDiv = document.getElementById('deploy-status');

            // Validation
            if (!username || username.length < 3) {
                statusDiv.innerHTML = '<div class="alert alert-danger">❌ Username must be at least 3 characters long</div>';
                return;
            }

            if (!password || password.length < 6) {
                statusDiv.innerHTML = '<div class="alert alert-danger">❌ Password must be at least 6 characters long</div>';
                return;
            }

            if (password !== confirm) {
                statusDiv.innerHTML = '<div class="alert alert-danger">❌ Passwords do not match</div>';
                return;
            }

            // Show progress
            statusDiv.innerHTML = `
                <div class="text-center">
                    <div class="spinner"></div>
                    <p>Creating database and admin account...</p>
                </div>
            `;

            // Simulate deployment process
            setTimeout(() => {
                // Create SQL file for database setup
                const sqlContent = `-- E-Voting System Database Setup
-- Generated by Deployment System

CREATE DATABASE IF NOT EXISTS voting_system;
USE voting_system;

-- Students table
CREATE TABLE IF NOT EXISTS students (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    matric_no VARCHAR(50) NOT NULL UNIQUE,
    department VARCHAR(255) NOT NULL,
    level ENUM('ND1', 'ND2', 'HND1', 'HND2') NOT NULL,
    password VARCHAR(255) NOT NULL,
    has_voted BOOLEAN DEFAULT FALSE,
    approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Positions table
CREATE TABLE IF NOT EXISTS positions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    position_name VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default positions
INSERT IGNORE INTO positions (position_name) VALUES
('President'),
('Director of Sports'),
('Director of Information');

-- Candidates table
CREATE TABLE IF NOT EXISTS candidates (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    position_id INT(11) NOT NULL,
    program VARCHAR(255) NOT NULL,
    photo VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (position_id) REFERENCES positions(id)
);

-- Votes table
CREATE TABLE IF NOT EXISTS votes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    candidate_id INT(11) NOT NULL,
    position_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (candidate_id) REFERENCES candidates(id),
    FOREIGN KEY (position_id) REFERENCES positions(id),
    UNIQUE KEY unique_vote (student_id, position_id)
);

-- Admin table
CREATE TABLE IF NOT EXISTS admins (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert admin account (password will be hashed in PHP)
-- Username: ${username}
-- Password: ${password}
-- Note: Run this through PHP to hash the password properly

-- Election Settings table
CREATE TABLE IF NOT EXISTS election_settings (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(50) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT IGNORE INTO election_settings (setting_key, setting_value) VALUES
('election_status', 'inactive'),
('election_title', 'Student Union Government Elections'),
('election_start_date', CURDATE()),
('election_end_date', DATE_ADD(CURDATE(), INTERVAL 1 WEEK));

-- Password Reset Requests table
CREATE TABLE IF NOT EXISTS password_reset_requests (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    token VARCHAR(255) NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id),
    UNIQUE KEY (student_id, status)
);`;

                // Create PHP file for admin creation
                const phpContent = `<?php
// Admin Account Creation Script
// Generated by Deployment System

$host = "localhost";
$username = "root";
$password = "";
$database = "voting_system";

try {
    $conn = new mysqli($host, $username, $password, $database);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    // Create admin account
    $admin_username = "${username}";
    $admin_password = password_hash("${password}", PASSWORD_DEFAULT);

    $stmt = $conn->prepare("INSERT INTO admins (username, password) VALUES (?, ?) ON DUPLICATE KEY UPDATE password = ?");
    $stmt->bind_param("sss", $admin_username, $admin_password, $admin_password);

    if ($stmt->execute()) {
        echo "Admin account created successfully!\\n";
        echo "Username: " . $admin_username . "\\n";
        echo "Password: ${password}\\n";
    } else {
        echo "Error creating admin account: " . $conn->error . "\\n";
    }

    $conn->close();
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\\n";
}
?>`;

                // Download SQL file
                const sqlBlob = new Blob([sqlContent], { type: 'text/plain' });
                const sqlUrl = window.URL.createObjectURL(sqlBlob);
                const sqlLink = document.createElement('a');
                sqlLink.href = sqlUrl;
                sqlLink.download = 'voting_system_database.sql';
                document.body.appendChild(sqlLink);
                sqlLink.click();
                document.body.removeChild(sqlLink);
                window.URL.revokeObjectURL(sqlUrl);

                // Download PHP file
                const phpBlob = new Blob([phpContent], { type: 'text/plain' });
                const phpUrl = window.URL.createObjectURL(phpBlob);
                const phpLink = document.createElement('a');
                phpLink.href = phpUrl;
                phpLink.download = 'create_admin.php';
                document.body.appendChild(phpLink);
                phpLink.click();
                document.body.removeChild(phpLink);
                window.URL.revokeObjectURL(phpUrl);

                deploymentData.username = username;
                deploymentData.password = password;

                statusDiv.innerHTML = `
                    <div class="alert alert-success">
                        <strong>✅ Database Files Created!</strong><br>
                        Two files have been downloaded:<br><br>
                        <strong>1. voting_system_database.sql</strong> - Database structure<br>
                        <strong>2. create_admin.php</strong> - Admin account creator<br><br>
                        <strong>Next Steps:</strong><br>
                        1. Import the SQL file into phpMyAdmin<br>
                        2. Run the PHP file to create admin account<br>
                        3. Copy your project files to C:\\xampp\\htdocs\\VOTE\\
                    </div>
                    <div class="text-center">
                        <button class="btn" onclick="completeDeployment()">✅ Files Imported - Complete Setup</button>
                    </div>
                `;
            }, 2000);
        }

        // Complete deployment
        function completeDeployment() {
            // Set up the portal links
            document.getElementById('student-portal-link').href = 'http://localhost/VOTE/';
            document.getElementById('admin-panel-link').href = 'http://localhost/VOTE/admin/';

            showStep(4);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we're running from file:// protocol
            if (window.location.protocol === 'file:') {
                // This is good - we're running as a standalone file
                console.log('Running as standalone HTML file');
            }
        });
    </script>
