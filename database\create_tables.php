<?php
// Include database configuration
$conn = require_once 'db_config.php';

// Create Students table
$sql = "CREATE TABLE IF NOT EXISTS students (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    matric_no VARCHAR(50) NOT NULL UNIQUE,
    department VARCHAR(255) NOT NULL,
    level ENUM('ND1', 'ND2', 'HND1', 'HND2') NOT NULL,
    password VARCHAR(255) NOT NULL,
    has_voted BOOLEAN DEFAULT FALSE,
    approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql) !== TRUE) {
    die("Error creating students table: " . $conn->error);
}

// Create Positions table
$sql = "CREATE TABLE IF NOT EXISTS positions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    position_name VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql) !== TRUE) {
    die("Error creating positions table: " . $conn->error);
}

// Insert default positions
$positions = ["President", "Director of Sports", "Director of Information"];
foreach ($positions as $position) {
    $sql = "INSERT IGNORE INTO positions (position_name) VALUES ('$position')";
    $conn->query($sql);
}

// Create Candidates table
$sql = "CREATE TABLE IF NOT EXISTS candidates (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    position_id INT(11) NOT NULL,
    program VARCHAR(255) NOT NULL,
    photo VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (position_id) REFERENCES positions(id)
)";

if ($conn->query($sql) !== TRUE) {
    die("Error creating candidates table: " . $conn->error);
}

// Create Votes table
$sql = "CREATE TABLE IF NOT EXISTS votes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    candidate_id INT(11) NOT NULL,
    position_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (candidate_id) REFERENCES candidates(id),
    FOREIGN KEY (position_id) REFERENCES positions(id),
    UNIQUE KEY unique_vote (student_id, position_id)
)";

if ($conn->query($sql) !== TRUE) {
    die("Error creating votes table: " . $conn->error);
}

// Create Admin table
$sql = "CREATE TABLE IF NOT EXISTS admins (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql) !== TRUE) {
    die("Error creating admins table: " . $conn->error);
}

// Insert default admin (username: admin, password: admin@2023)
$admin_username = "admin";
$admin_password = password_hash("admin@2023", PASSWORD_DEFAULT);
$sql = "INSERT IGNORE INTO admins (username, password) VALUES ('$admin_username', '$admin_password')";
$conn->query($sql);

// Create Election Settings table
$sql = "CREATE TABLE IF NOT EXISTS election_settings (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(50) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) !== TRUE) {
    die("Error creating election_settings table: " . $conn->error);
}

// Insert default settings
$default_settings = [
    ['election_status', 'inactive'], // active, inactive
    ['election_title', 'Student Union Government Elections'],
    ['election_start_date', date('Y-m-d')],
    ['election_end_date', date('Y-m-d', strtotime('+1 week'))]
];

foreach ($default_settings as $setting) {
    $key = $setting[0];
    $value = $setting[1];
    $sql = "INSERT IGNORE INTO election_settings (setting_key, setting_value) VALUES ('$key', '$value')";
    $conn->query($sql);
}

// Create Password Reset Requests table
$sql = "CREATE TABLE IF NOT EXISTS password_reset_requests (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    token VARCHAR(255) NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id),
    UNIQUE KEY (student_id, status)
)";

if ($conn->query($sql) !== TRUE) {
    die("Error creating password_reset_requests table: " . $conn->error);
}

echo "All tables created successfully!";
$conn->close();
?>
