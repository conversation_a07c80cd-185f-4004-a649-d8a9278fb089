<?php
// Start session
session_start();

// Redirect if already logged in
if (isset($_SESSION['student_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection
$conn = require_once 'database/db_config.php';

$error = '';

// Check if there are any approved reset requests for this student
$matric_no = isset($_POST['matric_no']) ? trim($_POST['matric_no']) : '';

// If matric_no is provided, check for approved requests
if (!empty($matric_no)) {
    $stmt = $conn->prepare("
        SELECT r.token
        FROM password_reset_requests r
        JOIN students s ON r.student_id = s.id
        WHERE s.matric_no = ? AND r.status = 'approved'
    ");
    $stmt->bind_param("s", $matric_no);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $reset_request = $result->fetch_assoc();
        $token = $reset_request['token'];

        // Redirect to complete_reset.php with the token
        header("Location: complete_reset.php?token=" . $token);
        exit;
    }
}

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $matric_no = trim($_POST['matric_no']);
    $password = $_POST['password'];

    // Validate input
    if (empty($matric_no) || empty($password)) {
        $error = 'Please enter both matriculation number and password';
    } else {
        // Check if approval_status column exists
        $column_check = $conn->query("SHOW COLUMNS FROM students LIKE 'approval_status'");
        $has_approval_column = $column_check->num_rows > 0;

        // Prepare the appropriate query based on column existence
        if ($has_approval_column) {
            $stmt = $conn->prepare("SELECT id, full_name, password, approval_status FROM students WHERE matric_no = ?");
        } else {
            $stmt = $conn->prepare("SELECT id, full_name, password FROM students WHERE matric_no = ?");

            // Try to add the column
            $conn->query("ALTER TABLE students ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending' AFTER has_voted");
            $conn->query("UPDATE students SET approval_status = 'approved'");
        }

        $stmt->bind_param("s", $matric_no);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 1) {
            $student = $result->fetch_assoc();

            // Check approval status if the column exists
            if ($has_approval_column) {
                if ($student['approval_status'] === 'pending') {
                    $error = 'Your account is still pending approval by an administrator. Please check back later.';
                    goto end_login_process;
                } elseif ($student['approval_status'] === 'rejected') {
                    $error = 'Your registration has been rejected. Please contact an administrator for assistance.';
                    goto end_login_process;
                }
            }

            // Verify password for approved accounts
            if (password_verify($password, $student['password'])) {
                // Set session variables
                $_SESSION['student_id'] = $student['id'];
                $_SESSION['student_name'] = $student['full_name'];

                // Extract first name (actually the second name as requested)
                $name_parts = explode(' ', $student['full_name']);
                if (count($name_parts) > 1) {
                    // Use the second part of the name (index 1)
                    $_SESSION['student_first_name'] = $name_parts[1];
                } else {
                    // If there's only one name, use that
                    $_SESSION['student_first_name'] = $name_parts[0];
                }

                // Redirect to voting page
                header('Location: vote.php');
                exit;
            } else {
                $error = 'Invalid password';
            }
        } else {
            $error = 'Matriculation number not found';
        }

        end_login_process:
    }
}

// Include header
include 'includes/header.php';
?>

<div class="login-container">
    <div class="card">
        <div class="card-header">
            <h4 class="mb-0">Student Login</h4>
        </div>
        <div class="card-body">
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="post" action="login.php">
                <div class="form-group">
                    <label for="matric_no">Matriculation Number</label>
                    <input type="text" class="form-control" id="matric_no" name="matric_no" required value="<?php echo isset($_POST['matric_no']) ? htmlspecialchars($_POST['matric_no']) : ''; ?>">
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary toggle-password" type="button" data-target="password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary btn-block">Login</button>
            </form>

            <div class="text-center mt-3">
                <p>Don't have an account? <a href="register.php">Register here</a></p>
                <p><a href="reset_password.php">Forgot your password?</a></p>
            </div>
        </div>
    </div>
</div>

<!-- Add JavaScript for password toggle -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggleButton = document.querySelector('.toggle-password');

    if (toggleButton) {
        toggleButton.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const passwordInput = document.getElementById(targetId);
            const icon = this.querySelector('i');

            // Toggle password visibility
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }
});
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
