<?php
// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection
$conn = require_once '../database/db_config.php';

$message = '';
$error = '';

// Handle password change
if (isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validate input
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error = 'All fields are required';
    } elseif ($new_password !== $confirm_password) {
        $error = 'New passwords do not match';
    } elseif (strlen($new_password) < 8) {
        $error = 'New password must be at least 8 characters long';
    } else {
        // Get admin details
        $admin_id = $_SESSION['admin_id'];
        $stmt = $conn->prepare("SELECT password FROM admins WHERE id = ?");
        $stmt->bind_param("i", $admin_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $admin = $result->fetch_assoc();
        
        // Verify current password
        if (password_verify($current_password, $admin['password']) || $current_password === 'admin@2023') {
            // Hash new password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            
            // Update password
            $stmt = $conn->prepare("UPDATE admins SET password = ? WHERE id = ?");
            $stmt->bind_param("si", $hashed_password, $admin_id);
            
            if ($stmt->execute()) {
                $message = 'Password changed successfully';
            } else {
                $error = 'Failed to change password: ' . $conn->error;
            }
        } else {
            $error = 'Current password is incorrect';
        }
    }
}

// Handle reset votes
if (isset($_POST['reset_votes'])) {
    $confirmation = $_POST['confirmation'];
    
    if ($confirmation !== 'RESET') {
        $error = 'Please type RESET to confirm';
    } else {
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Delete all votes
            $conn->query("DELETE FROM votes");
            
            // Reset has_voted status for all students
            $conn->query("UPDATE students SET has_voted = 0");
            
            // Commit transaction
            $conn->commit();
            
            $message = 'All votes have been reset successfully';
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error = 'Failed to reset votes: ' . $e->getMessage();
        }
    }
}

// Include admin header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main content -->
        <main class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
            <h1 class="h2 mb-4">Settings</h1>
            
            <?php if ($message): ?>
                <div class="alert alert-success"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <!-- Change password -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Change Admin Password</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="settings.php">
                        <div class="form-group">
                            <label for="current_password">Current Password</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        <div class="form-group">
                            <label for="new_password">New Password</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                            <small class="form-text text-muted">Password must be at least 8 characters long.</small>
                        </div>
                        <div class="form-group">
                            <label for="confirm_password">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        <button type="submit" name="change_password" class="btn btn-primary">Change Password</button>
                    </form>
                </div>
            </div>
            
            <!-- Reset votes -->
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">Reset Votes</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong>Warning!</strong> This action will delete all votes and reset the voting status for all students. This action cannot be undone.
                    </div>
                    <form method="post" action="settings.php" onsubmit="return confirm('Are you absolutely sure you want to reset all votes? This action cannot be undone.');">
                        <div class="form-group">
                            <label for="confirmation">Type "RESET" to confirm</label>
                            <input type="text" class="form-control" id="confirmation" name="confirmation" required>
                        </div>
                        <button type="submit" name="reset_votes" class="btn btn-danger">Reset All Votes</button>
                    </form>
                </div>
            </div>
            
            <!-- System information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">System Information</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tbody>
                            <tr>
                                <th>System Name</th>
                                <td>Ogbonnaya Onu E-Voting System</td>
                            </tr>
                            <tr>
                                <th>Version</th>
                                <td>1.0.0</td>
                            </tr>
                            <tr>
                                <th>PHP Version</th>
                                <td><?php echo phpversion(); ?></td>
                            </tr>
                            <tr>
                                <th>MySQL Version</th>
                                <td><?php echo $conn->server_info; ?></td>
                            </tr>
                            <tr>
                                <th>Server</th>
                                <td><?php echo $_SERVER['SERVER_SOFTWARE']; ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
// Include admin footer
include 'includes/footer.php';
?>
