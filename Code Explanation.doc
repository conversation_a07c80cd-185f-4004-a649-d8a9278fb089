# Ogbonnaya Onu E-Voting System - Code Explanation

This document explains what each file in the Ogbonnaya Onu E-Voting System does in simple terms.

## Root Directory Files

### index.php
This is the homepage of the website. It shows a welcome message, features of the voting system, and instructions on how to vote. It includes the header and footer from the includes folder.

### login.php
This is where students enter their matriculation number and password to log in. It checks if the student exists in the database and if their password is correct. If successful, it creates a session and redirects to the voting page.

### register.php
This page allows new students to create an account. It collects information like full name, matriculation number, department, and password. It checks if the matriculation number is already registered and saves the new student to the database.

### vote.php
This is the main voting page where students select candidates for different positions. It shows all available positions and candidates with their photos. Students can only vote once, and their choices are saved to the database.

### results.php
This page shows the election results with charts and numbers. It displays how many votes each candidate received and the percentage of total votes. The results update in real-time as votes come in.

### logout.php
This simple file ends the student's session and redirects them to the homepage. It's used when a student clicks the logout button.

### importdb.php
This is a special tool that helps set up the database with a single click. It creates all the necessary tables and default settings without requiring technical knowledge. Users just need to click the "Import Database" button, and everything is set up automatically.

### about.php
This page provides information about the school and the e-voting system. It explains the purpose of the system and who created it.

### contact.php
This page shows contact information for the school, including address, phone numbers, and social media links. Students can use this to get in touch with administrators.

## Folders and Their Files

### admin folder
This contains all files related to the administrator panel, where election officials manage the system.

#### admin/index.php
This is the admin login page. Administrators enter their username and password here to access the control panel. It creates a default admin account if none exists.

#### admin/dashboard.php
This is the main admin control panel. It shows statistics like registered students, votes cast, and voter turnout. It also has quick links to other admin functions.

#### admin/candidates.php
This page allows administrators to add, edit, or remove candidates. They can upload candidate photos and assign them to positions.

#### admin/students.php
This page shows a list of all registered students. Administrators can approve new registrations, reset passwords, or remove students.

#### admin/positions.php
This page lets administrators create and manage election positions like President, Secretary, etc. They can add new positions or edit existing ones.

#### admin/settings.php
This page allows administrators to change system settings like election start/end dates, admin password, and other configuration options.

#### admin/results.php
This shows detailed election results with more information than the student-facing results page. Administrators can see complete voting statistics here.

### api folder
This contains files that handle data exchange between the website and the database.

#### api/vote.php
This file processes votes when students submit them. It checks if the student is eligible to vote, records their choices, and returns a success or error message.

#### api/results.php
This file fetches the current vote counts from the database and formats them for display on the results page. It's used by the real-time results feature.

### css folder
This contains styling files that control how the website looks.

#### css/style.css
This is the main stylesheet that defines colors, fonts, spacing, and layout for the entire website. It makes everything look nice and organized.

### database folder
This contains files related to the database connection and structure.

#### database/db_config.php
This file contains the database connection settings (host, username, password, database name). It's used by other files to connect to the database.

#### database/voting_system.sql
This is the SQL file that creates all the necessary tables in the database. It's used during installation to set up the database structure.

### images folder
This contains all images used on the website, including logos, icons, and candidate photos.

### includes folder
This contains reusable code parts that are included in multiple pages.

#### includes/header.php
This file contains the top part of every page, including the navigation menu. It's included at the beginning of most pages.

#### includes/footer.php
This file contains the bottom part of every page, including copyright information and JavaScript links. It's included at the end of most pages.

#### includes/functions.php
This file contains helper functions used throughout the website, like formatting dates, validating input, and processing forms.

### js folder
This contains JavaScript files that add interactivity to the website.

#### js/main.js
This is the main JavaScript file that handles various interactive features like form validation, real-time updates, and chart creation.

#### js/realtime.js
This file handles the real-time updating of election results. It periodically checks for new votes and updates the display without refreshing the page.

### uploads folder
This is where uploaded files like candidate photos are stored. The system automatically saves files here when administrators upload them.

### websocket folder
This contains files for the real-time communication system that updates results instantly when new votes are cast.

## How It All Works Together

1. Students register accounts using register.php
2. Administrators approve student accounts using admin/students.php
3. Students log in using login.php
4. Students cast votes using vote.php
5. Votes are processed by api/vote.php and stored in the database
6. Results are displayed on results.php and updated in real-time using js/realtime.js
7. Administrators manage the entire process through the admin panel

The system uses a combination of PHP for server-side processing, MySQL for data storage, and JavaScript for interactive features. The files are organized in a way that separates different functions of the system, making it easier to maintain and update.
