# Ogbonnaya Onu E-Voting System - Installation Guide

This guide will help you set up the Ogbonnaya Onu E-Voting System on your computer.

## Prerequisites

Before you begin, make sure you have the following installed:
- XAMPP (or any other local server with PHP and MySQL)
- Web browser (Chrome, Firefox, Edge, etc.)

## Installation Steps

1. **Install XAMPP**
   - Download XAMPP from https://www.apachefriends.org/
   - Install XAMPP following the installation wizard
   - Make sure to include Apache, MySQL, and PHP components

2. **Place the VOTE folder in the right location**
   - Copy the VOTE folder to the htdocs directory of your XAMPP installation
   - The path should be: C:\xampp\htdocs\VOTE (for Windows)
   - For Mac: /Applications/XAMPP/htdocs/VOTE
   - For Linux: /opt/lampp/htdocs/VOTE

3. **Start the XAMPP services**
   - Open the XAMPP Control Panel
   - Start the Apache and MySQL services by clicking the "Start" buttons

4. **Set up the database (Easy Method)**
   - Open your web browser and go to http://localhost/VOTE/importdb.php
   - Click the "Import Database" button
   - Wait for the success message confirming the database has been created
   - This will automatically create the database, all necessary tables, and set up the default admin account

   OR

   **Set up the database (Manual Method)**
   - Open your web browser and go to http://localhost/phpmyadmin
   - Click on "New" in the left sidebar to create a new database
   - Name the database "voting_system" and click "Create"
   - In the VOTE folder, locate the database/create_tables.php file
   - Run this file by visiting http://localhost/VOTE/database/create_tables.php in your browser

6. **Configure the database connection**
   - Open the file database/db_config.php in a text editor
   - Make sure the database connection details match your setup:
     - $host = "localhost";
     - $username = "root";
     - $password = ""; (this is usually empty by default in XAMPP)
     - $database = "voting_system";

7. **Access the website**
   - Open your web browser and go to http://localhost/VOTE
   - You should see the homepage of the Ogbonnaya Onu E-Voting System

8. **Access the admin panel**
   - Go to http://localhost/VOTE/admin
   - Default admin credentials:
     - Username: admin
     - Password: admin@2023

## Troubleshooting

If you encounter any issues during installation:

1. **Database connection error**
   - Make sure MySQL service is running in XAMPP
   - Check that the database name is "voting_system"
   - Verify the database connection details in database/db_config.php

2. **Page not found error**
   - Make sure Apache service is running in XAMPP
   - Check that the VOTE folder is in the correct location (htdocs)
   - Try accessing http://localhost to verify Apache is working

3. **Permission issues**
   - Make sure the web server has read and write permissions to the VOTE folder
   - For uploads to work, ensure the uploads directory has write permissions

4. **PHP errors**
   - Check the XAMPP error logs at C:\xampp\apache\logs\error.log
   - Make sure your PHP version is compatible (PHP 7.0 or higher recommended)

## System Requirements

- PHP 7.0 or higher
- MySQL 5.6 or higher
- Modern web browser with JavaScript enabled

## Additional Notes

- The system is configured to work with a local installation by default
- For a production environment, you should change the admin password immediately
- Regular backups of the database are recommended to prevent data loss
