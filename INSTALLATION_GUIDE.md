# E-Voting System Installation Guide

## 📋 Table of Contents
1. [System Requirements](#system-requirements)
2. [Pre-Installation Setup](#pre-installation-setup)
3. [Installation Steps](#installation-steps)
4. [Post-Installation Configuration](#post-installation-configuration)
5. [Troubleshooting](#troubleshooting)
6. [Security Recommendations](#security-recommendations)

---

## 🖥️ System Requirements

### Minimum Requirements
- **Operating System:** Windows 7/8/10/11, macOS 10.12+, or Linux Ubuntu 16.04+
- **Web Server:** Apache 2.4+ (included in XAMPP/WAMP)
- **Database:** MySQL 5.7+ or MariaDB 10.2+
- **PHP:** Version 7.4 or higher
- **RAM:** 2GB minimum, 4GB recommended
- **Storage:** 500MB free disk space
- **Browser:** Chrome 70+, Firefox 65+, Safari 12+, Edge 79+

### Recommended Setup
- **XAMPP** (Cross-platform) - Includes Apache, MySQL, PHP, and phpMyAdmin
- **WAMP** (Windows only) - Alternative to XAMPP for Windows users
- **LAMP** (Linux) - For Linux server deployments

---

## 🚀 Pre-Installation Setup

### Step 1: Download and Install XAMPP
1. **Download XAMPP:**
   - Visit: https://www.apachefriends.org/
   - Download the latest version for your operating system
   - Choose the version with PHP 7.4 or higher

2. **Install XAMPP:**
   - Run the installer as Administrator (Windows) or with sudo (Linux/Mac)
   - Install to default location: `C:\xampp` (Windows) or `/opt/lampp` (Linux)
   - Select all components (Apache, MySQL, PHP, phpMyAdmin)

3. **Start XAMPP Services:**
   - Open XAMPP Control Panel
   - Start **Apache** and **MySQL** services
   - Ensure both services show "Running" status

### Step 2: Verify Installation
1. **Test Apache:**
   - Open browser and go to: `http://localhost`
   - You should see the XAMPP welcome page

2. **Test MySQL:**
   - Go to: `http://localhost/phpmyadmin`
   - You should see the phpMyAdmin interface

3. **Test PHP:**
   - Create a file named `test.php` in `C:\xampp\htdocs\`
   - Add content: `<?php phpinfo(); ?>`
   - Visit: `http://localhost/test.php`
   - You should see PHP information page

---

## 📦 Installation Steps

### Step 1: Download E-Voting System
1. **Extract Files:**
   - Extract the e-voting system files
   - Copy all files to: `C:\xampp\htdocs\VOTE\`
   - Ensure the folder structure looks like:
     ```
     C:\xampp\htdocs\VOTE\
     ├── setup.php
     ├── index.php
     ├── admin/
     ├── includes/
     ├── css/
     ├── js/
     ├── images/
     └── uploads/
     ```

### Step 2: Set Folder Permissions
**For Windows:**
- Right-click on the `VOTE` folder
- Properties → Security → Edit
- Give "Full Control" to "Users" group

**For Linux/Mac:**
```bash
sudo chmod -R 755 /opt/lampp/htdocs/VOTE/
sudo chmod -R 777 /opt/lampp/htdocs/VOTE/uploads/
sudo chown -R www-data:www-data /opt/lampp/htdocs/VOTE/
```

### Step 3: Run the Setup
1. **Access Setup Page:**
   - Open your web browser
   - Go to: `http://localhost/VOTE/setup.php`

2. **Follow Setup Wizard:**
   - **Step 1:** Review system requirements and features
   - **Step 2:** Database creation (automatic)
   - **Step 3:** Create admin account
   - **Step 4:** Setup completion

3. **Database Setup (Automatic):**
   - The setup will automatically create:
     - Database: `voting_system`
     - Tables: students, admins, positions, candidates, votes, election_settings, password_reset_requests
     - Default positions: President, Director of Sports, Director of Information
     - Default election settings

4. **Admin Account Creation:**
   - Enter your desired admin username (default: admin)
   - Create a strong password (minimum 6 characters)
   - Confirm the password
   - Click "Create Admin Account"

### Step 4: Verify Installation
1. **Check Database:**
   - Go to: `http://localhost/phpmyadmin`
   - Verify `voting_system` database exists
   - Check that all tables are created

2. **Test Student Portal:**
   - Go to: `http://localhost/VOTE/`
   - You should see the student login/registration page

3. **Test Admin Panel:**
   - Go to: `http://localhost/VOTE/admin/`
   - Login with your admin credentials
   - Verify admin dashboard loads correctly

---

## ⚙️ Post-Installation Configuration

### Step 1: Security Setup
1. **Delete Setup File:**
   ```bash
   # Delete the setup file for security
   rm C:\xampp\htdocs\VOTE\setup.php
   ```

2. **Change Default Passwords:**
   - Change MySQL root password in phpMyAdmin
   - Update database credentials in configuration files if needed

### Step 2: Configure Election Settings
1. **Access Admin Panel:**
   - Login to admin panel
   - Go to "Election Settings"

2. **Configure Basic Settings:**
   - Election title
   - Start and end dates
   - Voting rules and regulations

3. **Add Positions:**
   - Go to "Manage Positions"
   - Add/edit voting positions as needed

### Step 3: System Configuration
1. **Upload Institution Logo:**
   - Place logo file in `images/` folder
   - Update references in configuration

2. **Customize Appearance:**
   - Modify CSS files in `css/` folder
   - Update colors, fonts, and styling as needed

3. **Configure Email Settings (Optional):**
   - Set up SMTP for password reset emails
   - Configure notification settings

---

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. "Access Denied" Error
**Problem:** Cannot access setup.php or admin panel
**Solution:**
- Ensure XAMPP Apache and MySQL are running
- Check file permissions
- Verify URL is correct: `http://localhost/VOTE/setup.php`

#### 2. Database Connection Failed
**Problem:** Cannot connect to MySQL database
**Solution:**
- Start MySQL service in XAMPP Control Panel
- Check MySQL is running on port 3306
- Verify database credentials in setup.php

#### 3. "404 Not Found" Error
**Problem:** Pages not loading
**Solution:**
- Ensure files are in correct directory: `C:\xampp\htdocs\VOTE\`
- Check Apache is running
- Verify .htaccess files are present

#### 4. PHP Errors
**Problem:** PHP warnings or errors displayed
**Solution:**
- Ensure PHP version is 7.4 or higher
- Check PHP extensions are enabled
- Review error logs in XAMPP

#### 5. Upload Issues
**Problem:** Cannot upload candidate photos
**Solution:**
- Check `uploads/` folder permissions
- Ensure folder is writable
- Verify PHP upload settings

### Getting Help
- Check XAMPP documentation
- Review PHP error logs
- Ensure all system requirements are met
- Contact technical support if issues persist

---

## 🔒 Security Recommendations

### Essential Security Steps
1. **Delete Setup Files:**
   - Remove `setup.php` after installation
   - Delete any test files

2. **Strong Passwords:**
   - Use complex admin passwords
   - Require strong student passwords
   - Change default MySQL passwords

3. **File Permissions:**
   - Set appropriate folder permissions
   - Restrict access to sensitive files
   - Secure configuration files

4. **Regular Updates:**
   - Keep XAMPP updated
   - Update PHP to latest stable version
   - Monitor for security patches

5. **Backup Strategy:**
   - Regular database backups
   - File system backups
   - Test restore procedures

### Production Deployment
For production use:
- Use dedicated web server (not XAMPP)
- Enable HTTPS/SSL certificates
- Configure firewall rules
- Set up monitoring and logging
- Implement regular security audits

---

## 📞 Support Information

### Technical Support
- **Institution:** Ogbonnaya Onu Polytechnic
- **System:** E-Voting Platform
- **Version:** 1.0.0

### Quick Reference URLs
- **Student Portal:** `http://localhost/VOTE/`
- **Admin Panel:** `http://localhost/VOTE/admin/`
- **Database Admin:** `http://localhost/phpmyadmin`
- **XAMPP Control:** Start → XAMPP Control Panel

### System Status Check
To verify system is working:
1. XAMPP services running ✓
2. Database accessible ✓
3. Student portal loads ✓
4. Admin panel accessible ✓
5. File uploads working ✓

---

**Installation Complete!** 🎉

Your E-Voting System is now ready for use. Students can register and vote, while administrators can manage elections through the admin panel.
