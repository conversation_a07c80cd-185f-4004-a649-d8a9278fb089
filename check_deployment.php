<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Database configuration
$host = "localhost";
$username = "root";
$password = "";
$database = "voting_system";

// Response function
function sendResponse($deployed, $message = '', $data = null) {
    echo json_encode([
        'deployed' => $deployed,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

try {
    // Check if deployment status file exists
    $deploymentStatusFile = 'deployment_status.json';
    
    if (!file_exists($deploymentStatusFile)) {
        sendResponse(false, 'Deployment not started');
    }
    
    $deploymentStatus = json_decode(file_get_contents($deploymentStatusFile), true);
    
    if (!$deploymentStatus) {
        sendResponse(false, 'Invalid deployment status file');
    }
    
    // Check if deployment is completed
    if (isset($deploymentStatus['deployment_completed']) && $deploymentStatus['deployment_completed']) {
        sendResponse(true, 'Deployment completed', $deploymentStatus);
    }
    
    // Check database status
    if (!isset($deploymentStatus['database_created']) || !$deploymentStatus['database_created']) {
        sendResponse(false, 'Database not created');
    }
    
    // Try to connect to database to verify it exists
    $conn = new mysqli($host, $username, $password, $database);
    
    if ($conn->connect_error) {
        sendResponse(false, 'Database connection failed');
    }
    
    // Check if essential tables exist
    $requiredTables = ['students', 'admins', 'positions', 'candidates', 'votes', 'election_settings'];
    $existingTables = [];
    
    foreach ($requiredTables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            $existingTables[] = $table;
        }
    }
    
    if (count($existingTables) !== count($requiredTables)) {
        $missingTables = array_diff($requiredTables, $existingTables);
        sendResponse(false, 'Missing tables: ' . implode(', ', $missingTables));
    }
    
    // Check if admin exists
    $result = $conn->query("SELECT COUNT(*) as count FROM admins");
    $adminCount = $result->fetch_assoc()['count'];
    
    if ($adminCount == 0) {
        sendResponse(false, 'No admin account found', [
            'database_ready' => true,
            'admin_needed' => true
        ]);
    }
    
    $conn->close();
    
    // If we get here, everything is set up
    sendResponse(true, 'System fully deployed', $deploymentStatus);
    
} catch (Exception $e) {
    sendResponse(false, 'Error checking deployment status: ' . $e->getMessage());
}
?>
