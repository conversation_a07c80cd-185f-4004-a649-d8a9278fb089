<?php
// Start session
session_start();

// Include database connection
$conn = require_once 'database/db_config.php';

// Get positions and candidates with vote counts
$positions = [];
$sql = "SELECT id, position_name FROM positions ORDER BY id";
$positions_result = $conn->query($sql);

while ($position = $positions_result->fetch_assoc()) {
    $position_id = $position['id'];
    $candidates = [];

    $sql = "SELECT c.id, c.full_name, c.program, c.photo, COUNT(v.id) as votes
            FROM candidates c
            LEFT JOIN votes v ON c.id = v.candidate_id
            WHERE c.position_id = $position_id
            GROUP BY c.id
            ORDER BY votes DESC";

    $candidates_result = $conn->query($sql);

    while ($candidate = $candidates_result->fetch_assoc()) {
        $candidates[] = $candidate;
    }

    $positions[] = [
        'id' => $position['id'],
        'name' => $position['position_name'],
        'candidates' => $candidates
    ];
}

// Get total votes
$sql = "SELECT COUNT(*) as total FROM votes";
$result = $conn->query($sql);
$total_votes = $result->fetch_assoc()['total'];

// Get total registered students
$sql = "SELECT COUNT(*) as total FROM students";
$result = $conn->query($sql);
$total_students = $result->fetch_assoc()['total'];

// Calculate voter turnout
$voter_turnout = $total_students > 0 ? round(($total_votes / $total_students) * 100) : 0;

// Include header
include 'includes/header.php';
?>

<?php if (isset($_SESSION['student_id'])): ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Election Results</h2>
    <div class="text-right">
        <h5 class="text-primary">Welcome, <?php echo isset($_SESSION['student_first_name']) ? htmlspecialchars($_SESSION['student_first_name']) : htmlspecialchars($_SESSION['student_name']); ?>!</h5>
    </div>
</div>
<?php else: ?>
<h2 class="text-center mb-4">Election Results</h2>
<?php endif; ?>

<div class="alert alert-info text-center">
    <div class="row">
        <div class="col-md-4">
            <strong>Total Votes:</strong> <?php echo $total_votes; ?>
        </div>
        <div class="col-md-4">
            <strong>Registered Students:</strong> <?php echo $total_students; ?>
        </div>
        <div class="col-md-4">
            <strong>Voter Turnout:</strong> <?php echo $voter_turnout; ?>%
        </div>
    </div>
</div>

<div class="results-container">
    <?php foreach ($positions as $position): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0"><?php echo $position['name']; ?></h4>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <?php foreach ($position['candidates'] as $candidate): ?>
                            <?php
                            $total_position_votes = array_sum(array_column($position['candidates'], 'votes'));
                            $percentage = $total_position_votes > 0 ? round(($candidate['votes'] / $total_position_votes) * 100) : 0;
                            ?>
                            <div class="media mb-3">
                                <img src="<?php echo $candidate['photo']; ?>" class="mr-3 rounded-circle" alt="<?php echo $candidate['full_name']; ?>" style="width: 60px; height: 60px; object-fit: cover;">
                                <div class="media-body">
                                    <h5 class="mt-0"><?php echo $candidate['full_name']; ?></h5>
                                    <p class="text-muted mb-1">Program: <?php echo $candidate['program']; ?></p>
                                    <div class="progress">
                                        <div id="progress-<?php echo $candidate['id']; ?>" class="progress-bar" role="progressbar" style="width: <?php echo $percentage; ?>%;" aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="d-flex justify-content-between mt-1">
                                        <span><strong id="vote-count-<?php echo $candidate['id']; ?>"><?php echo $candidate['votes']; ?></strong> votes</span>
                                        <span id="vote-percent-<?php echo $candidate['id']; ?>"><?php echo $percentage; ?>%</span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="col-md-4">
                        <div class="chart-container">
                            <canvas id="chart-<?php echo $position['id']; ?>"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<script>
// Initialize charts when the page loads
document.addEventListener('DOMContentLoaded', function() {
    <?php foreach ($positions as $position): ?>
        new Chart(document.getElementById('chart-<?php echo $position['id']; ?>'), {
            type: 'pie',
            data: {
                labels: [<?php echo implode(', ', array_map(function($candidate) { return "'" . addslashes($candidate['full_name']) . "'"; }, $position['candidates'])); ?>],
                datasets: [{
                    data: [<?php echo implode(', ', array_map(function($candidate) { return $candidate['votes']; }, $position['candidates'])); ?>],
                    backgroundColor: [
                        'rgba(0, 123, 255, 0.7)',
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: [
                        'rgba(0, 123, 255, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${label}: ${value} votes (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    <?php endforeach; ?>
});
</script>

<!-- Include the real-time update script -->
<script src="js/realtime.js"></script>

<?php
// Include footer
include 'includes/footer.php';
?>
