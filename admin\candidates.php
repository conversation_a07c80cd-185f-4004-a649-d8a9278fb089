<?php
// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection
$conn = require_once '../database/db_config.php';

// Handle form submissions
$message = '';
$error = '';

// Add new candidate
if (isset($_POST['add_candidate'])) {
    $full_name = trim($_POST['full_name']);
    $position_id = $_POST['position_id'];
    $program = trim($_POST['program']);
    
    // Validate input
    if (empty($full_name) || empty($position_id) || empty($program)) {
        $error = 'All fields are required';
    } else {
        // Handle file upload
        $target_dir = "../uploads/candidates/";
        
        // Create directory if it doesn't exist
        if (!file_exists($target_dir)) {
            mkdir($target_dir, 0777, true);
        }
        
        $photo = '';
        
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
            $file_tmp = $_FILES['photo']['tmp_name'];
            $file_name = basename($_FILES['photo']['name']);
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
            
            // Generate unique filename
            $new_file_name = uniqid() . '.' . $file_ext;
            $target_file = $target_dir . $new_file_name;
            
            // Check if file is an image
            $check = getimagesize($file_tmp);
            if ($check !== false) {
                // Check file size (max 5MB)
                if ($_FILES['photo']['size'] <= 5000000) {
                    // Allow certain file formats
                    if (in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])) {
                        if (move_uploaded_file($file_tmp, $target_file)) {
                            $photo = 'uploads/candidates/' . $new_file_name;
                        } else {
                            $error = 'Failed to upload image';
                        }
                    } else {
                        $error = 'Only JPG, JPEG, PNG & GIF files are allowed';
                    }
                } else {
                    $error = 'File is too large (max 5MB)';
                }
            } else {
                $error = 'File is not an image';
            }
        } else {
            $error = 'Please select an image';
        }
        
        if (empty($error)) {
            // Insert candidate
            $stmt = $conn->prepare("INSERT INTO candidates (full_name, position_id, program, photo) VALUES (?, ?, ?, ?)");
            $stmt->bind_param("siss", $full_name, $position_id, $program, $photo);
            
            if ($stmt->execute()) {
                $message = 'Candidate added successfully';
            } else {
                $error = 'Failed to add candidate: ' . $conn->error;
            }
        }
    }
}

// Delete candidate
if (isset($_GET['delete'])) {
    $candidate_id = $_GET['delete'];
    
    // Get candidate photo
    $stmt = $conn->prepare("SELECT photo FROM candidates WHERE id = ?");
    $stmt->bind_param("i", $candidate_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 1) {
        $candidate = $result->fetch_assoc();
        $photo_path = '../' . $candidate['photo'];
        
        // Delete photo file
        if (file_exists($photo_path)) {
            unlink($photo_path);
        }
        
        // Delete candidate
        $stmt = $conn->prepare("DELETE FROM candidates WHERE id = ?");
        $stmt->bind_param("i", $candidate_id);
        
        if ($stmt->execute()) {
            $message = 'Candidate deleted successfully';
        } else {
            $error = 'Failed to delete candidate: ' . $conn->error;
        }
    }
}

// Get all positions
$positions = [];
$sql = "SELECT id, position_name FROM positions ORDER BY id";
$positions_result = $conn->query($sql);

while ($position = $positions_result->fetch_assoc()) {
    $positions[] = $position;
}

// Get all candidates
$candidates = [];
$sql = "SELECT c.id, c.full_name, c.program, c.photo, p.position_name, p.id as position_id
        FROM candidates c
        JOIN positions p ON c.position_id = p.id
        ORDER BY p.id, c.full_name";
$candidates_result = $conn->query($sql);

while ($candidate = $candidates_result->fetch_assoc()) {
    $candidates[] = $candidate;
}

// Include admin header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main content -->
        <main class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
            <h1 class="h2 mb-4">Manage Candidates</h1>
            
            <?php if ($message): ?>
                <div class="alert alert-success"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <!-- Add new candidate form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Add New Candidate</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="candidates.php" enctype="multipart/form-data">
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="full_name">Full Name</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="position_id">Position</label>
                                <select class="form-control" id="position_id" name="position_id" required>
                                    <option value="">Select Position</option>
                                    <?php foreach ($positions as $position): ?>
                                        <option value="<?php echo $position['id']; ?>"><?php echo $position['position_name']; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="program">Program</label>
                                <input type="text" class="form-control" id="program" name="program" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="photo">Photo</label>
                                <input type="file" class="form-control-file" id="photo" name="photo" required>
                                <small class="form-text text-muted">Upload a square image (1:1 ratio) for best results. Max size: 5MB.</small>
                            </div>
                        </div>
                        <button type="submit" name="add_candidate" class="btn btn-primary">Add Candidate</button>
                    </form>
                </div>
            </div>
            
            <!-- Candidates list -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Current Candidates</h5>
                </div>
                <div class="card-body">
                    <?php if (count($candidates) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Photo</th>
                                        <th>Name</th>
                                        <th>Position</th>
                                        <th>Program</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($candidates as $candidate): ?>
                                        <tr>
                                            <td>
                                                <img src="../<?php echo $candidate['photo']; ?>" alt="<?php echo $candidate['full_name']; ?>" class="rounded-circle" width="50" height="50" style="object-fit: cover;">
                                            </td>
                                            <td><?php echo $candidate['full_name']; ?></td>
                                            <td><?php echo $candidate['position_name']; ?></td>
                                            <td><?php echo $candidate['program']; ?></td>
                                            <td>
                                                <a href="edit_candidate.php?id=<?php echo $candidate['id']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                                <a href="candidates.php?delete=<?php echo $candidate['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this candidate?')">
                                                    <i class="fas fa-trash"></i> Delete
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-center">No candidates have been added yet.</p>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
// Include admin footer
include 'includes/footer.php';
?>
