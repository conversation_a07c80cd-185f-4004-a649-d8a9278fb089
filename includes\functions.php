<?php
/**
 * Helper functions for the e-voting system
 */

/**
 * Get a setting value from the election_settings table
 *
 * @param string $key The setting key
 * @param mixed $default Default value if setting not found
 * @return mixed The setting value
 */
function getSetting($key, $default = null) {
    global $conn;

    if (!isset($conn) || !$conn) {
        // Direct database connection
        $host = "localhost";
        $username = "root";
        $password = "";
        $database = "voting_system";

        // Connect to database
        try {
            $conn = new mysqli($host, $username, $password, $database);

            if ($conn->connect_error) {
                die("Connection failed: " . $conn->connect_error);
            }
        } catch (Exception $e) {
            die("Database connection error: " . $e->getMessage());
        }
    }

    // Check if election_settings table exists
    $result = $conn->query("SHOW TABLES LIKE 'election_settings'");
    if ($result->num_rows == 0) {
        return $default;
    }

    $stmt = $conn->prepare("SELECT setting_value FROM election_settings WHERE setting_key = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        return $result->fetch_assoc()['setting_value'];
    }

    return $default;
}

/**
 * Update a setting value in the election_settings table
 *
 * @param string $key The setting key
 * @param string $value The new setting value
 * @return bool True if successful, false otherwise
 */
function updateSetting($key, $value) {
    global $conn;

    if (!isset($conn) || !$conn) {
        // Direct database connection
        $host = "localhost";
        $username = "root";
        $password = "";
        $database = "voting_system";

        // Connect to database
        try {
            $conn = new mysqli($host, $username, $password, $database);

            if ($conn->connect_error) {
                return false;
            }
        } catch (Exception $e) {
            return false;
        }

        // Check if election_settings table exists
        $result = $conn->query("SHOW TABLES LIKE 'election_settings'");
        if ($result->num_rows == 0) {
            return false;
        }
    }

    // Check if setting exists
    $stmt = $conn->prepare("SELECT id FROM election_settings WHERE setting_key = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        // Update existing setting
        $stmt = $conn->prepare("UPDATE election_settings SET setting_value = ? WHERE setting_key = ?");
        $stmt->bind_param("ss", $value, $key);
        return $stmt->execute();
    }

    // Insert new setting
    $stmt = $conn->prepare("INSERT INTO election_settings (setting_key, setting_value) VALUES (?, ?)");
    $stmt->bind_param("ss", $key, $value);
    return $stmt->execute();
}

/**
 * Check if the election is active
 *
 * @return bool True if election is active, false otherwise
 */
function isElectionActive() {
    return getSetting('election_status', 'inactive') === 'active';
}

/**
 * Generate a random token
 *
 * @param int $length The length of the token
 * @return string The generated token
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Create a password reset request
 *
 * @param int $student_id The student ID
 * @return string|bool The reset token if successful, false otherwise
 */
function createPasswordResetRequest($student_id) {
    global $conn;

    if (!isset($conn) || !$conn) {
        $conn = require_once __DIR__ . '/../database/db_config.php';
    }

    // Delete any existing pending requests
    $stmt = $conn->prepare("DELETE FROM password_reset_requests WHERE student_id = ? AND status = 'pending'");
    $stmt->bind_param("i", $student_id);
    $stmt->execute();

    // Generate a new token
    $token = generateToken();

    // Insert new request
    $stmt = $conn->prepare("INSERT INTO password_reset_requests (student_id, token, status) VALUES (?, ?, 'pending')");
    $stmt->bind_param("is", $student_id, $token);

    if ($stmt->execute()) {
        return $token;
    }

    return false;
}

/**
 * Get a password reset request by token
 *
 * @param string $token The reset token
 * @return array|bool The request data if found, false otherwise
 */
function getPasswordResetRequest($token) {
    global $conn;

    if (!isset($conn) || !$conn) {
        $conn = require_once __DIR__ . '/../database/db_config.php';
    }

    $stmt = $conn->prepare("
        SELECT r.*, s.full_name, s.matric_no
        FROM password_reset_requests r
        JOIN students s ON r.student_id = s.id
        WHERE r.token = ?
    ");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * Update the status of a password reset request
 *
 * @param string $token The reset token
 * @param string $status The new status (pending, approved, rejected, completed)
 * @return bool True if successful, false otherwise
 */
function updatePasswordResetStatus($token, $status) {
    global $conn;

    if (!isset($conn) || !$conn) {
        $conn = require_once __DIR__ . '/../database/db_config.php';
    }

    $stmt = $conn->prepare("UPDATE password_reset_requests SET status = ? WHERE token = ?");
    $stmt->bind_param("ss", $status, $token);

    return $stmt->execute();
}

/**
 * Get all pending password reset requests
 *
 * @return array The pending requests
 */
function getPendingPasswordResetRequests() {
    global $conn;

    if (!isset($conn) || !$conn) {
        $conn = require_once __DIR__ . '/../database/db_config.php';
    }

    $sql = "
        SELECT r.*, s.full_name, s.matric_no
        FROM password_reset_requests r
        JOIN students s ON r.student_id = s.id
        WHERE r.status = 'pending'
        ORDER BY r.created_at DESC
    ";
    $result = $conn->query($sql);

    $requests = [];
    while ($row = $result->fetch_assoc()) {
        $requests[] = $row;
    }

    return $requests;
}

/**
 * Get student details by ID
 *
 * @param int $student_id The student ID
 * @return array|bool The student details if found, false otherwise
 */
function getStudentById($student_id) {
    global $conn;

    if (!isset($conn) || !$conn) {
        $conn = require_once __DIR__ . '/../database/db_config.php';
    }

    $stmt = $conn->prepare("SELECT * FROM students WHERE id = ?");
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        return $result->fetch_assoc();
    }

    return false;
}
