' E-Voting System Deployment Launcher
' Ogbonnaya Onu Polytechnic
' VBScript Version - No special permissions required

Option Explicit

Dim objShell, objFSO, objWMI
Dim xamppPath, projectPath, projectName
Dim htdocsPath, deployUrl
Dim apacheRunning, mysqlRunning

' Initialize objects
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
Set objWMI = GetObject("winmgmts:")

' Configuration
xamppPath = "C:\xampp"
projectName = "VOTE"
projectPath = objFSO.GetParentFolderName(WScript.ScriptFullName)

' Show welcome message
MsgBox "E-Voting System Deployment Launcher" & vbCrLf & vbCrLf & _
       "Ogbonnaya Onu Polytechnic" & vbCrLf & _
       "This will automatically start XAMPP and deploy your e-voting system.", _
       vbInformation, "E-Voting System Deployment"

' Check if XAMPP exists
If Not objFSO.FolderExists(xamppPath) Then
    MsgBox "XAMPP not found at " & xamppPath & vbCrLf & vbCrLf & _
           "Please install XAMPP first:" & vbCrLf & _
           "https://www.apachefriends.org/" & vbCrLf & vbCrLf & _
           "Or update the xamppPath in this script if installed elsewhere.", _
           vbCritical, "XAMPP Not Found"
    WScript.Quit
End If

' Check XAMPP components
If Not objFSO.FileExists(xamppPath & "\apache\bin\httpd.exe") Then
    MsgBox "Apache not found in XAMPP installation!" & vbCrLf & _
           "Please reinstall XAMPP.", vbCritical, "Apache Not Found"
    WScript.Quit
End If

If Not objFSO.FileExists(xamppPath & "\mysql\bin\mysqld.exe") Then
    MsgBox "MySQL not found in XAMPP installation!" & vbCrLf & _
           "Please reinstall XAMPP.", vbCritical, "MySQL Not Found"
    WScript.Quit
End If

' Function to check if process is running
Function IsProcessRunning(processName)
    Dim processes, process
    Set processes = objWMI.ExecQuery("SELECT * FROM Win32_Process WHERE Name = '" & processName & "'")
    IsProcessRunning = (processes.Count > 0)
End Function

' Function to start a process
Function StartProcess(exePath, arguments)
    On Error Resume Next
    If arguments = "" Then
        objShell.Run """" & exePath & """", 0, False
    Else
        objShell.Run """" & exePath & """ " & arguments, 0, False
    End If
    StartProcess = (Err.Number = 0)
    On Error GoTo 0
End Function

' Function to copy files
Function CopyProjectFiles()
    On Error Resume Next
    
    Dim sourceFile, destPath
    htdocsPath = xamppPath & "\htdocs\" & projectName
    
    ' Create destination folder if it doesn't exist
    If Not objFSO.FolderExists(htdocsPath) Then
        objFSO.CreateFolder(htdocsPath)
    End If
    
    ' Copy all files except script files
    Dim file
    For Each file In objFSO.GetFolder(projectPath).Files
        If LCase(objFSO.GetExtensionName(file.Name)) <> "vbs" And _
           LCase(objFSO.GetExtensionName(file.Name)) <> "bat" And _
           LCase(objFSO.GetExtensionName(file.Name)) <> "ps1" Then
            objFSO.CopyFile file.Path, htdocsPath & "\" & file.Name, True
        End If
    Next
    
    ' Copy subdirectories
    Dim folder
    For Each folder In objFSO.GetFolder(projectPath).SubFolders
        objFSO.CopyFolder folder.Path, htdocsPath & "\" & folder.Name, True
    Next
    
    CopyProjectFiles = (Err.Number = 0)
    On Error GoTo 0
End Function

' Check service status
apacheRunning = IsProcessRunning("httpd.exe")
mysqlRunning = IsProcessRunning("mysqld.exe")

Dim statusMsg
statusMsg = "Current Status:" & vbCrLf & _
           "Apache: " & IIf(apacheRunning, "Running", "Stopped") & vbCrLf & _
           "MySQL: " & IIf(mysqlRunning, "Running", "Stopped") & vbCrLf & vbCrLf

If Not apacheRunning Or Not mysqlRunning Then
    statusMsg = statusMsg & "Starting required services..."
    MsgBox statusMsg, vbInformation, "Service Status"
Else
    statusMsg = statusMsg & "All services are already running!"
    MsgBox statusMsg, vbInformation, "Service Status"
End If

' Start Apache if not running
If Not apacheRunning Then
    If StartProcess(xamppPath & "\apache\bin\httpd.exe", "") Then
        WScript.Sleep 3000 ' Wait 3 seconds
        If IsProcessRunning("httpd.exe") Then
            ' Apache started successfully
        Else
            MsgBox "Failed to start Apache!" & vbCrLf & _
                   "Please start XAMPP manually.", vbCritical, "Apache Start Failed"
            WScript.Quit
        End If
    Else
        MsgBox "Could not start Apache!" & vbCrLf & _
               "Please start XAMPP manually.", vbCritical, "Apache Start Error"
        WScript.Quit
    End If
End If

' Start MySQL if not running
If Not mysqlRunning Then
    Dim mysqlArgs
    mysqlArgs = "--defaults-file=""" & xamppPath & "\mysql\bin\my.ini"""
    If StartProcess(xamppPath & "\mysql\bin\mysqld.exe", mysqlArgs) Then
        WScript.Sleep 5000 ' Wait 5 seconds
        If IsProcessRunning("mysqld.exe") Then
            ' MySQL started successfully
        Else
            MsgBox "Failed to start MySQL!" & vbCrLf & _
                   "Please start XAMPP manually.", vbCritical, "MySQL Start Failed"
            WScript.Quit
        End If
    Else
        MsgBox "Could not start MySQL!" & vbCrLf & _
               "Please start XAMPP manually.", vbCritical, "MySQL Start Error"
        WScript.Quit
    End If
End If

' Copy project files
MsgBox "Setting up project files...", vbInformation, "File Setup"

If CopyProjectFiles() Then
    MsgBox "Project files copied successfully!", vbInformation, "File Setup Complete"
Else
    MsgBox "Warning: Some files may not have been copied." & vbCrLf & _
           "The deployment may still work.", vbExclamation, "File Copy Warning"
End If

' Wait a moment for services to stabilize
WScript.Sleep 2000

' Construct deployment URL
deployUrl = "http://localhost/" & projectName & "/deploy.php"

' Show ready message
Dim readyMsg
readyMsg = "Deployment Ready!" & vbCrLf & vbCrLf & _
          "Services Status:" & vbCrLf & _
          "✓ Apache Web Server: Running" & vbCrLf & _
          "✓ MySQL Database: Running" & vbCrLf & _
          "✓ Project Files: Copied" & vbCrLf & vbCrLf & _
          "The deployment page will now open in your browser." & vbCrLf & vbCrLf & _
          "URL: " & deployUrl

MsgBox readyMsg, vbInformation, "Ready to Deploy"

' Open deployment page in browser
objShell.Run deployUrl

' Show final instructions
Dim finalMsg
finalMsg = "Deployment interface launched!" & vbCrLf & vbCrLf & _
          "If the page didn't open, manually go to:" & vbCrLf & _
          deployUrl & vbCrLf & vbCrLf & _
          "After successful deployment, you can access:" & vbCrLf & _
          "• Student Portal: http://localhost/" & projectName & "/" & vbCrLf & _
          "• Admin Panel: http://localhost/" & projectName & "/admin/" & vbCrLf & vbCrLf & _
          "Would you like to stop XAMPP services when you're done?"

Dim stopServices
stopServices = MsgBox(finalMsg, vbYesNo + vbQuestion, "Deployment Complete")

If stopServices = vbYes Then
    ' Stop services
    On Error Resume Next
    objShell.Run "taskkill /F /IM httpd.exe", 0, True
    objShell.Run "taskkill /F /IM mysqld.exe", 0, True
    On Error GoTo 0
    
    MsgBox "XAMPP services have been stopped." & vbCrLf & _
           "Thank you for using the E-Voting System!", vbInformation, "Services Stopped"
Else
    MsgBox "XAMPP services are still running." & vbCrLf & _
           "You can stop them manually when needed." & vbCrLf & vbCrLf & _
           "Thank you for using the E-Voting System!", vbInformation, "Deployment Complete"
End If

' Helper function for IIf (since VBScript doesn't have it)
Function IIf(condition, trueValue, falseValue)
    If condition Then
        IIf = trueValue
    Else
        IIf = falseValue
    End If
End Function
