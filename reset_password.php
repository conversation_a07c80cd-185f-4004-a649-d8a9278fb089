<?php
// Start session
session_start();

// Redirect if already logged in
if (isset($_SESSION['student_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection and functions
$conn = require_once 'database/db_config.php';
require_once 'includes/functions.php';

$error = '';
$success = '';

// Check if there are any approved reset requests for this student
$matric_no = isset($_POST['matric_no']) ? trim($_POST['matric_no']) : '';

// If matric_no is provided, check for approved requests
if (!empty($matric_no)) {
    $stmt = $conn->prepare("
        SELECT r.token
        FROM password_reset_requests r
        JOIN students s ON r.student_id = s.id
        WHERE s.matric_no = ? AND r.status = 'approved'
    ");
    $stmt->bind_param("s", $matric_no);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $reset_request = $result->fetch_assoc();
        $token = $reset_request['token'];

        // Redirect to complete_reset.php with the token
        header("Location: complete_reset.php?token=" . $token);
        exit;
    }
}

// Process reset password request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $matric_no = trim($_POST['matric_no']);

    // Validate input
    if (empty($matric_no)) {
        $error = 'Please enter your matriculation number';
    } else {
        // Check if matric number exists
        $stmt = $conn->prepare("SELECT id, full_name FROM students WHERE matric_no = ?");
        $stmt->bind_param("s", $matric_no);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 1) {
            $student = $result->fetch_assoc();
            $student_id = $student['id'];

            // Check if there's already a pending request
            $stmt = $conn->prepare("
                SELECT id FROM password_reset_requests
                WHERE student_id = ? AND status = 'pending'
            ");
            $stmt->bind_param("i", $student_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $success = 'Your password reset request has already been submitted. Please wait for admin approval.';
            } else {
                // Create password reset request
                $token = createPasswordResetRequest($student_id);

                if ($token) {
                    $success = 'Your password reset request has been submitted. Please wait for admin approval.';
                } else {
                    $error = 'Failed to create password reset request. Please try again later.';
                }
            }
        } else {
            $error = 'Matriculation number not found';
        }
    }
}

// Include header
include 'includes/header.php';
?>

<div class="login-container">
    <div class="card">
        <div class="card-header">
            <h4 class="mb-0">Reset Password</h4>
        </div>
        <div class="card-body">
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
                <div class="text-center mt-3">
                    <a href="login.php" class="btn btn-primary">Back to Login</a>
                </div>
            <?php else: ?>
                <p>Enter your matriculation number to request a password reset. An administrator will need to approve your request before you can reset your password.</p>

                <form method="post" action="reset_password.php">
                    <div class="form-group">
                        <label for="matric_no">Matriculation Number</label>
                        <input type="text" class="form-control" id="matric_no" name="matric_no" required value="<?php echo isset($_POST['matric_no']) ? htmlspecialchars($_POST['matric_no']) : ''; ?>">
                    </div>

                    <button type="submit" class="btn btn-primary btn-block">Request Password Reset</button>
                </form>

                <div class="text-center mt-3">
                    <a href="login.php">Back to Login</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
