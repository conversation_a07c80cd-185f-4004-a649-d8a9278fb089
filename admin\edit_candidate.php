<?php
// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection
$conn = require_once '../database/db_config.php';

// Check if candidate ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: candidates.php');
    exit;
}

$candidate_id = $_GET['id'];

// Get candidate details
$stmt = $conn->prepare("SELECT * FROM candidates WHERE id = ?");
$stmt->bind_param("i", $candidate_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: candidates.php');
    exit;
}

$candidate = $result->fetch_assoc();

// Get all positions
$positions = [];
$sql = "SELECT id, position_name FROM positions ORDER BY id";
$positions_result = $conn->query($sql);

while ($position = $positions_result->fetch_assoc()) {
    $positions[] = $position;
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name']);
    $position_id = $_POST['position_id'];
    $program = trim($_POST['program']);
    
    // Validate input
    if (empty($full_name) || empty($position_id) || empty($program)) {
        $error = 'All fields are required';
    } else {
        // Check if photo is being updated
        $photo = $candidate['photo'];
        
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
            $target_dir = "../uploads/candidates/";
            
            // Create directory if it doesn't exist
            if (!file_exists($target_dir)) {
                mkdir($target_dir, 0777, true);
            }
            
            $file_tmp = $_FILES['photo']['tmp_name'];
            $file_name = basename($_FILES['photo']['name']);
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
            
            // Generate unique filename
            $new_file_name = uniqid() . '.' . $file_ext;
            $target_file = $target_dir . $new_file_name;
            
            // Check if file is an image
            $check = getimagesize($file_tmp);
            if ($check !== false) {
                // Check file size (max 5MB)
                if ($_FILES['photo']['size'] <= 5000000) {
                    // Allow certain file formats
                    if (in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])) {
                        if (move_uploaded_file($file_tmp, $target_file)) {
                            // Delete old photo if it exists
                            if (!empty($candidate['photo'])) {
                                $old_photo_path = '../' . $candidate['photo'];
                                if (file_exists($old_photo_path)) {
                                    unlink($old_photo_path);
                                }
                            }
                            
                            $photo = 'uploads/candidates/' . $new_file_name;
                        } else {
                            $error = 'Failed to upload image';
                        }
                    } else {
                        $error = 'Only JPG, JPEG, PNG & GIF files are allowed';
                    }
                } else {
                    $error = 'File is too large (max 5MB)';
                }
            } else {
                $error = 'File is not an image';
            }
        }
        
        if (empty($error)) {
            // Update candidate
            $stmt = $conn->prepare("UPDATE candidates SET full_name = ?, position_id = ?, program = ?, photo = ? WHERE id = ?");
            $stmt->bind_param("sissi", $full_name, $position_id, $program, $photo, $candidate_id);
            
            if ($stmt->execute()) {
                $message = 'Candidate updated successfully';
                
                // Refresh candidate data
                $stmt = $conn->prepare("SELECT * FROM candidates WHERE id = ?");
                $stmt->bind_param("i", $candidate_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $candidate = $result->fetch_assoc();
            } else {
                $error = 'Failed to update candidate: ' . $conn->error;
            }
        }
    }
}

// Include admin header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main content -->
        <main class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center mb-3">
                <h1 class="h2">Edit Candidate</h1>
                <a href="candidates.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Candidates
                </a>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-success"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            <img src="../<?php echo $candidate['photo']; ?>" alt="<?php echo $candidate['full_name']; ?>" class="img-fluid rounded mb-3" style="max-height: 300px;">
                            <h5><?php echo $candidate['full_name']; ?></h5>
                        </div>
                        <div class="col-md-8">
                            <form method="post" action="edit_candidate.php?id=<?php echo $candidate_id; ?>" enctype="multipart/form-data">
                                <div class="form-group">
                                    <label for="full_name">Full Name</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo htmlspecialchars($candidate['full_name']); ?>" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="position_id">Position</label>
                                    <select class="form-control" id="position_id" name="position_id" required>
                                        <?php foreach ($positions as $position): ?>
                                            <option value="<?php echo $position['id']; ?>" <?php echo $candidate['position_id'] == $position['id'] ? 'selected' : ''; ?>>
                                                <?php echo $position['position_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="program">Program</label>
                                    <input type="text" class="form-control" id="program" name="program" value="<?php echo htmlspecialchars($candidate['program']); ?>" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="photo">Photo</label>
                                    <input type="file" class="form-control-file" id="photo" name="photo">
                                    <small class="form-text text-muted">Leave empty to keep the current photo. Upload a square image (1:1 ratio) for best results. Max size: 5MB.</small>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">Update Candidate</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
// Include admin footer
include 'includes/footer.php';
?>
