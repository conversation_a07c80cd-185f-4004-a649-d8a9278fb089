# Disable directory listing
Options -Indexes

# Protect .htaccess file
<Files .htaccess>
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

# Protect database configuration file
<Files db_config.php>
    Order Allow,Deny
    Deny from all
</Files>

# Protect websocket server file
<Files server.php>
    Order Allow,Deny
    Deny from all
</Files>

# Enable rewriting
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /

    # Redirect to HTTPS (uncomment in production)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Prevent direct access to PHP files in certain directories
    RewriteRule ^database/.*\.php$ - [F,L]
    RewriteRule ^includes/.*\.php$ - [F,L]
    RewriteRule ^websocket/.*\.php$ - [F,L]
</IfModule>

# PHP settings
<IfModule mod_php7.c>
    # Disable showing PHP errors to users
    php_flag display_errors off
    
    # Increase security
    php_flag register_globals off
    php_flag magic_quotes_gpc off
    
    # Set maximum upload file size
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
</IfModule>

# Add proper MIME types
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
</IfModule>

# Enable browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Compress files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript application/json
</IfModule>
