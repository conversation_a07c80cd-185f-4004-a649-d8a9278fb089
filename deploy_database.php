<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Database configuration
$host = "localhost";
$username = "root";
$password = "";
$database = "voting_system";

// Response function
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Get request data
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['action'])) {
    sendResponse(false, 'Invalid request');
}

if ($input['action'] === 'create_database') {
    try {
        // Step 1: Connect to MySQL server without selecting a database
        $conn = new mysqli($host, $username, $password);

        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }

        // Step 2: Create database if it doesn't exist
        $sql = "CREATE DATABASE IF NOT EXISTS $database";
        if (!$conn->query($sql)) {
            throw new Exception("Error creating database: " . $conn->error);
        }

        // Step 3: Select the database
        $conn->select_db($database);

        // Step 4: Create Students table
        $sql = "CREATE TABLE IF NOT EXISTS students (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(255) NOT NULL,
            matric_no VARCHAR(50) NOT NULL UNIQUE,
            department VARCHAR(255) NOT NULL,
            level ENUM('ND1', 'ND2', 'HND1', 'HND2') NOT NULL,
            password VARCHAR(255) NOT NULL,
            has_voted BOOLEAN DEFAULT FALSE,
            approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating students table: " . $conn->error);
        }

        // Step 5: Create Positions table
        $sql = "CREATE TABLE IF NOT EXISTS positions (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            position_name VARCHAR(255) NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating positions table: " . $conn->error);
        }

        // Step 6: Insert default positions
        $positions = ["President", "Director of Sports", "Director of Information"];
        foreach ($positions as $position) {
            $sql = "INSERT IGNORE INTO positions (position_name) VALUES ('$position')";
            $conn->query($sql);
        }

        // Step 7: Create Candidates table
        $sql = "CREATE TABLE IF NOT EXISTS candidates (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(255) NOT NULL,
            position_id INT(11) NOT NULL,
            program VARCHAR(255) NOT NULL,
            photo VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (position_id) REFERENCES positions(id)
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating candidates table: " . $conn->error);
        }

        // Step 8: Create Votes table
        $sql = "CREATE TABLE IF NOT EXISTS votes (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            student_id INT(11) NOT NULL,
            candidate_id INT(11) NOT NULL,
            position_id INT(11) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(id),
            FOREIGN KEY (candidate_id) REFERENCES candidates(id),
            FOREIGN KEY (position_id) REFERENCES positions(id),
            UNIQUE KEY unique_vote (student_id, position_id)
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating votes table: " . $conn->error);
        }

        // Step 9: Create Admin table
        $sql = "CREATE TABLE IF NOT EXISTS admins (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating admins table: " . $conn->error);
        }

        // Step 10: Create Election Settings table
        $sql = "CREATE TABLE IF NOT EXISTS election_settings (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(50) NOT NULL UNIQUE,
            setting_value TEXT NOT NULL,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating election_settings table: " . $conn->error);
        }

        // Step 11: Insert default settings
        $default_settings = [
            ['election_status', 'inactive'], // active, inactive
            ['election_title', 'Student Union Government Elections'],
            ['election_start_date', date('Y-m-d')],
            ['election_end_date', date('Y-m-d', strtotime('+1 week'))]
        ];

        foreach ($default_settings as $setting) {
            $key = $setting[0];
            $value = $setting[1];
            $sql = "INSERT IGNORE INTO election_settings (setting_key, setting_value) VALUES ('$key', '$value')";

            if (!$conn->query($sql)) {
                throw new Exception("Error inserting default settings: " . $conn->error);
            }
        }

        // Step 12: Create Password Reset Requests table
        $sql = "CREATE TABLE IF NOT EXISTS password_reset_requests (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            student_id INT(11) NOT NULL,
            token VARCHAR(255) NOT NULL,
            status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(id),
            UNIQUE KEY (student_id, status)
        )";

        if (!$conn->query($sql)) {
            throw new Exception("Error creating password_reset_requests table: " . $conn->error);
        }

        // Step 13: Create deployment status file
        $deploymentStatus = [
            'database_created' => true,
            'created_at' => date('Y-m-d H:i:s'),
            'version' => '1.0.0'
        ];
        
        file_put_contents('deployment_status.json', json_encode($deploymentStatus, JSON_PRETTY_PRINT));

        $conn->close();

        sendResponse(true, "Database created successfully! All tables have been set up and configured.");

    } catch (Exception $e) {
        sendResponse(false, $e->getMessage());
    }
} else {
    sendResponse(false, 'Invalid action');
}
?>
