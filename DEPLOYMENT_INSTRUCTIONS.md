# E-Voting System - One-Click Deployment

## 🚀 Quick Start - Just Double-Click!

You now have **3 easy ways** to deploy your e-voting system without manually starting XAMPP or typing URLs:

### Option 1: VBScript Launcher (Recommended) ⭐
**File:** `Deploy_E-Voting_System.vbs`
- ✅ **Easiest to use** - just double-click
- ✅ **No special permissions** required
- ✅ **User-friendly dialogs** guide you through the process
- ✅ **Works on all Windows versions**

**How to use:**
1. Double-click `Deploy_E-Voting_System.vbs`
2. Follow the dialog boxes
3. Your browser will automatically open the deployment page

### Option 2: Batch File Launcher
**File:** `Deploy_E-Voting_System.bat`
- ✅ **Command-line interface** with colored output
- ✅ **Detailed status messages**
- ⚠️ May need "Run as Administrator" for best results

**How to use:**
1. Right-click `Deploy_E-Voting_System.bat`
2. Select "Run as administrator" (recommended)
3. Follow the on-screen instructions

### Option 3: PowerShell Launcher (Advanced)
**File:** `Deploy_E-Voting_System.ps1`
- ✅ **Most advanced features**
- ✅ **Best error handling**
- ⚠️ May require execution policy changes

**How to use:**
1. Right-click `Deploy_E-Voting_System.ps1`
2. Select "Run with PowerShell"
3. If blocked, run: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

## 📋 What These Launchers Do Automatically

1. **Check XAMPP Installation**
   - Verify XAMPP is installed at `C:\xampp`
   - Check Apache and MySQL components

2. **Start Required Services**
   - Start Apache web server (if not running)
   - Start MySQL database server (if not running)

3. **Setup Project Files**
   - Copy all project files to `C:\xampp\htdocs\VOTE\`
   - Update files if already exists

4. **Launch Deployment Interface**
   - Open `http://localhost/VOTE/deploy.php` in your browser
   - Guide you through the deployment process

5. **Optional Service Management**
   - Ask if you want to stop services when done

## 🔧 Requirements

- **XAMPP** installed at `C:\xampp` (default location)
- **Windows** operating system
- **Internet connection** (for downloading Bootstrap/FontAwesome)

## 📁 File Structure After Deployment

```
C:\xampp\htdocs\VOTE\
├── deploy.php              (Deployment interface)
├── index.php               (Student portal)
├── admin/                  (Admin panel)
├── includes/               (Shared components)
├── css/                    (Stylesheets)
├── js/                     (JavaScript files)
└── images/                 (Images and assets)
```

## 🌐 Access URLs After Deployment

- **Deployment Interface:** `http://localhost/VOTE/deploy.php`
- **Student Portal:** `http://localhost/VOTE/`
- **Admin Panel:** `http://localhost/VOTE/admin/`

## ❓ Troubleshooting

### XAMPP Not Found
- Install XAMPP from: https://www.apachefriends.org/
- Or edit the script to point to your XAMPP location

### Services Won't Start
- Check if ports 80 (Apache) and 3306 (MySQL) are free
- Close Skype, IIS, or other programs using these ports
- Run the launcher as Administrator

### Browser Doesn't Open
- Manually go to: `http://localhost/VOTE/deploy.php`
- Check if Apache is running in XAMPP Control Panel

### Permission Errors
- Run the launcher as Administrator
- Check Windows Defender/Antivirus settings

## 🔒 Security Notes

- **Delete deployment files** after successful setup:
  - `deploy.php`
  - `deploy_database.php`
  - `deploy_admin.php`
  - `check_deployment.php`
  - All launcher files (`.vbs`, `.bat`, `.ps1`)

- **Change default passwords** immediately after deployment

## 📞 Support

If you encounter any issues:
1. Check the troubleshooting section above
2. Ensure XAMPP is properly installed
3. Try running as Administrator
4. Contact technical support via the Support page

---

**Ogbonnaya Onu Polytechnic E-Voting System**  
*Excellence in Technology*
